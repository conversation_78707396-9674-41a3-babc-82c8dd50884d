import { Action, Property, integrationFetch } from '@opendashboard-inc/integration-core';

type Inputs = {
  domain: string;
};

type Auth = {
  apiKey: string;
};

export const organizationEnrichment: Action<Inputs, Auth> = {
  identifier: 'organizationEnrichment',
  displayName: 'Organization Enrichment',
  description: 'Enrich organization data using Apollo API',
  intent: 'read',
  props: {
    domain: Property.ShortText({
      displayName: 'Domain',
      description: 'Company domain (e.g., microsoft.com)',
      required: true,
    }),
  },
  run: async ({ auth, propsValue }) => {
    console.log('🔓 [APOLLO ENRICHMENT] ==========================================');
    console.log('🔓 [APOLLO ENRICHMENT] 🚀 STARTING ORGANIZATION ENRICHMENT');
    console.log('🔓 [APOLLO ENRICHMENT] ==========================================');

    // Clean the domain - remove extra spaces and quotes
    let actualDomain = propsValue.domain;
    if (typeof actualDomain === 'string') {
      actualDomain = actualDomain.trim().replace(/^["']|["']$/g, '');
    }

    console.log('🔓 [APOLLO ENRICHMENT] Raw domain:', JSON.stringify(propsValue.domain));
    console.log('🔓 [APOLLO ENRICHMENT] Cleaned domain:', actualDomain);

    if (!actualDomain) {
      throw new Error('Domain is required for organization enrichment');
    }

    // Build query parameters for Apollo API
    const queryParams = new URLSearchParams();
    queryParams.set('domain', actualDomain);

    // Build the full URL with query parameters
    const baseUrl = 'https://api.apollo.io/api/v1/organizations/enrich';
    const url = `${baseUrl}?${queryParams.toString()}`;

    console.log('🔓 [APOLLO ENRICHMENT] Calling Apollo API:');
    console.log('🔓 [APOLLO ENRICHMENT] URL:', url);
    console.log('🔓 [APOLLO ENRICHMENT] Method: GET');
    console.log('🔓 [APOLLO ENRICHMENT] Domain:', actualDomain);

    const res = await integrationFetch(url, {
      method: 'GET',
      headers: {
        'Cache-Control': 'no-cache',
        'X-Api-Key': auth.apiKey,
      },
    });

    if (!res.ok) {
      const errorText = await res.text();
      console.error('🔓 [APOLLO ENRICHMENT] API Error:', res.status, errorText);
      throw new Error(`Apollo API error (${res.status}): ${errorText}`);
    }

    const result = await res.json();
    console.log('🔓 [APOLLO ENRICHMENT] Success Response:', JSON.stringify(result, null, 2));

    return result;
  },
};