"use client"

import React, { useState, use<PERSON>em<PERSON>, useEffect } from "react"
import { But<PERSON> } from "@ui/components/ui/button"
import { Input } from "@ui/components/ui/input"
import { Checkbox } from "@ui/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@ui/components/ui/table"
import { ScrollArea } from "@ui/components/ui/scroll-area"
import { PlusIcon, UserGroupIcon, EnvelopeIcon, PhoneIcon, EllipsisVerticalIcon, TrashIcon, ChevronRightIcon, ChartLineIcon, DatabaseIcon, CodeMergeIcon, UpRightFromSquareIcon, MagnifyingGlassIcon } from "@ui/components/icons/FontAwesomeRegular"
import { MagnifyingGlassCircleIcon } from "@heroicons/react/24/outline"
import { UnlockEmailModal } from "@ui/components/workspace/main/common/unlockEmail"
import { UnlockPhoneModal } from "@ui/components/workspace/main/common/unlockPhone"
import { Pop<PERSON>, <PERSON>overContent, PopoverTrigger } from "@ui/components/ui/popover"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuGroup } from "@ui/components/ui/dropdown-menu"
import { DbRecordFilter, Match } from "@repo/app-db-utils/src/typings/db"
import { getMyCompanyLeads } from "@ui/api/leads"
import { Lead } from "@ui/typings/lead"
import { useAlert } from "@ui/providers/alert"
import { useRouter, useParams } from "@ui/context/routerContext"

interface MyLeadsProps {
    onLeadCreated?: (lead: any) => void
    token?: string
    workspaceId?: string
}

// Helper components
const ActionButton = ({ 
    icon: Icon, 
    children, 
    onClick 
}: { 
    icon: React.ComponentType<{ className?: string }>; 
    children: React.ReactNode; 
    onClick: () => void; 
}) => (
    <Button
        variant="outline"
        size="sm"
        onClick={onClick}
        className="text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center"
    >
        <Icon className="size-3" />
        <span className="truncate">{children}</span>
    </Button>
)

const ViewLinksModal = ({ trigger, links }: { trigger: React.ReactNode, links: any[] }) => (
    <Popover>
        <PopoverTrigger asChild>{trigger}</PopoverTrigger>
        <PopoverContent className="w-64 p-0 rounded-none border border-neutral-200 bg-white shadow-lg" align="end" sideOffset={4}>
            <div className="flex items-center justify-between p-3 border-b border-neutral-200">
                <h3 className="text-xs font-semibold text-black">Contact links</h3>
            </div>
            <div className="p-0">
                {links.map((link) => (
                    <a key={link.id} href={link.url} target="_blank" rel="noopener noreferrer" className="flex items-center justify-between p-3 hover:bg-neutral-50 transition-colors cursor-pointer">
                        <span className="text-xs text-primary font-semibold">{link.title}</span>
                        <UpRightFromSquareIcon className="size-3 text-neutral-400" />
                    </a>
                ))}
            </div>
        </PopoverContent>
    </Popover>
)

const LeadActionsDropdown = ({ trigger, onSendEmail, onAddToSegments, onAddToDatabase, onAddToWorkflow }: { 
    trigger: React.ReactNode, 
    onSendEmail?: () => void,
    onAddToSegments?: () => void,
    onAddToDatabase?: () => void,
    onAddToWorkflow?: () => void
}) => (
    <DropdownMenu>
        <DropdownMenuTrigger asChild>{trigger}</DropdownMenuTrigger>
        <DropdownMenuContent className="w-56 rounded-none text-neutral-800 font-semibold" align="end" sideOffset={4}>
            <DropdownMenuGroup className="p-1 flex flex-col gap-2">
                <DropdownMenuItem className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer" onClick={onSendEmail}>
                    <EnvelopeIcon className="size-3 text-neutral-600" />
                    <span>Send Email</span>
                </DropdownMenuItem>
                <DropdownMenuItem className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer" onClick={onAddToSegments}>
                    <ChartLineIcon className="size-3 text-neutral-600" />
                    <span>Add to Segments</span>
                </DropdownMenuItem>
                <DropdownMenuItem className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer" onClick={onAddToDatabase}>
                    <DatabaseIcon className="size-3 text-neutral-600" />
                    <span>Add to Database</span>
                </DropdownMenuItem>
                <DropdownMenuItem className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer" onClick={onAddToWorkflow}>
                    <CodeMergeIcon className="size-3 text-neutral-600" />
                    <span>Add to Workflow</span>
                </DropdownMenuItem>
            </DropdownMenuGroup>
        </DropdownMenuContent>
    </DropdownMenu>
)

const MyLeads = ({ onLeadCreated, token, workspaceId }: MyLeadsProps) => {
    const router = useRouter()
    const params = useParams()
    const { toast } = useAlert()
    
    // State management
    const [leads, setLeads] = useState<Lead[]>([])
    const [selectedLeads, setSelectedLeads] = useState<string[]>([])
    const [searchQuery, setSearchQuery] = useState("")
    const [filter, setFilter] = useState<DbRecordFilter>({ match: Match.All, conditions: [] })
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    
    // Modal states
    const [emailModalOpen, setEmailModalOpen] = useState(false)
    const [phoneModalOpen, setPhoneModalOpen] = useState(false)
    const [selectedLeadId, setSelectedLeadId] = useState<string>('')
    

    
    // Fetch my company leads when token and workspaceId are available
    useEffect(() => {
        const fetchMyCompanyLeads = async () => {
            if (!token || !workspaceId) return;
            
            setLoading(true);
            setError(null);
            
            try {
                const response = await getMyCompanyLeads(token, workspaceId, {
                    page: 1,
                    limit: 100, // Get reasonable amount for local filtering
                    search: searchQuery || undefined
                });
                
                if (response.isSuccess && response.data?.data?.leads) {
                    setLeads(response.data.data.leads);
                } else {
                    setError(response.error || "Failed to load my company leads");
                    toast.error("Error", {
                        description: response.error || "Failed to load my company leads"
                    });
                }
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : "An unknown error occurred";
                setError(errorMessage);
                toast.error("Error", {
                    description: errorMessage
                });
            } finally {
                setLoading(false);
            }
        };
        
        fetchMyCompanyLeads();
    }, [token, workspaceId, searchQuery, toast]);
    
    const handleSelectAll = (checked: boolean) => {
        setSelectedLeads(checked ? filteredLeads.map(lead => lead.id) : [])
    }
    
    const handleSelectLead = (leadId: string, checked: boolean) => {
        setSelectedLeads(checked ? [...selectedLeads, leadId] : selectedLeads.filter(id => id !== leadId))
    }
    
    const handleUnlockEmail = (leadId: string) => {
        setSelectedLeadId(leadId)
        setEmailModalOpen(true)
    }
    
    const handleUnlockPhone = (leadId: string) => {
        setSelectedLeadId(leadId)
        setPhoneModalOpen(true)
    }
    
    const handleUpgrade = () => {
        setEmailModalOpen(false)
        setPhoneModalOpen(false)
    }
    
    // Generate contact links based on real lead data
    const getContactLinks = (lead: Lead) => {
        const links = []
        
        // Type guard to ensure we're working with company data
        const isCompanyLead = lead?.type === 'company';
        const apolloCompanyData = isCompanyLead ? (lead.apolloData as any) : null;
        
        // Add company LinkedIn URL if available
        if (apolloCompanyData?.linkedin_url) {
            links.push({ 
                id: "company-linkedin", 
                title: "Company LinkedIn", 
                url: apolloCompanyData.linkedin_url 
            })
        } else if (lead.normalizedData?.company) {
            // Generate LinkedIn search if direct URL not available
            links.push({ 
                id: "company-linkedin-search", 
                title: "Find on LinkedIn", 
                url: `https://linkedin.com/search/results/companies/?keywords=${encodeURIComponent(lead.normalizedData.company)}` 
            })
        }
        
        // Add company website if available
        if (apolloCompanyData?.website_url) {
            links.push({ 
                id: "company-website", 
                title: "Company Website", 
                url: apolloCompanyData.website_url 
            })
        } else if (lead.normalizedData?.companyDomain) {
            links.push({ 
                id: "company-website", 
                title: "Company Website", 
                url: `https://${lead.normalizedData.companyDomain}` 
            })
        } else if (lead.normalizedData?.company) {
            // Generate company search if direct URL not available
            links.push({ 
                id: "company-search", 
                title: "Find Company Website", 
                url: `https://www.google.com/search?q=${encodeURIComponent(lead.normalizedData.company + ' official website')}` 
            })
        }
        
        // Add Crunchbase URL if available
        if (apolloCompanyData?.crunchbase_url) {
            links.push({ 
                id: "crunchbase", 
                title: "Crunchbase Profile", 
                url: apolloCompanyData.crunchbase_url 
            })
        }
        
        return links
    }

    const handleNameClick = (lead: Lead) => {
        // Navigate to company details page using router
        const domain = params.domain
        router.push(`/${domain}/lead-generation/companies/details/${lead.id}`)
    }
    
    const filteredLeads = useMemo(() => {
        let filtered = leads
        if (searchQuery?.trim()) {
            const searchTerm = searchQuery.trim().toLowerCase()
            filtered = filtered.filter(lead => 
                Object.values(lead).some(value => 
                    typeof value === 'string' && value.toLowerCase().includes(searchTerm)
                )
            )
        }
        return filtered
    }, [leads, searchQuery])

    return (
        <>
            <div className="flex items-center justify-between px-3 h-10 border-b border-neutral-200 bg-white">
                <div className="flex items-center">
                    <h1 className="text-xs font-semibold text-black">My Companies</h1>
                </div>
                <div className="flex items-center space-x-2">
                    {selectedLeads.length > 0 && (
                        <Button variant="ghost" className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium text-red-600 hover:text-red-700 hover:bg-red-50 cursor-pointer" onClick={() => { setLeads(leads.filter(lead => !selectedLeads.includes(lead.id))); setSelectedLeads([]) }}>
                            <TrashIcon className="size-3"/>Delete {selectedLeads.length}
                        </Button>
                    )}

                    <div className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start flex hover:bg-neutral-100 focus:bg-neutral-100 active:bg-neutral-100 items-center whitespace-nowrap font-medium cursor-pointer">
                        <MagnifyingGlassCircleIcon className="size-4"/>
                        <Input placeholder="Search company contacts" value={searchQuery} onChange={e => setSearchQuery(e.target.value)} className="text-xs transition-all outline-none h-auto !p-0 !ring-0 w-12 focus:w-48 !bg-transparent border-0 shadow-none drop-shadow-none placeholder:text-muted-foreground" />
                    </div>
                </div>
            </div>
            
            {/* Main Content Area */}
            <div className="flex-1 flex overflow-hidden">

                
                {/* Table Container */}
                <div className="flex-1 overflow-hidden">
                    {(loading || error || filteredLeads.length === 0) ? (
                        /* Empty State */
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                                {loading ? (
                                    <>
                                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
                                        <p className="text-sm text-muted-foreground">Loading my companies...</p>
                                    </>
                                ) : error ? (
                                    <>
                                        <UserGroupIcon className="size-12 text-neutral-400 mx-auto mb-4" />
                                        <h3 className="text-lg font-medium text-neutral-900 mb-2">Error loading companies</h3>
                                        <p className="text-red-600 text-sm mb-4">{error}</p>
                                    </>
                                ) : (
                                    <>
                                        <MagnifyingGlassIcon className="size-12 text-neutral-400 mx-auto mb-4" />
                                        <h3 className="text-lg font-medium text-neutral-900 mb-2">
                                            {searchQuery ? "No companies found" : "No companies in your leads yet"}
                                        </h3>
                                        <p className="text-neutral-500 mb-4 text-sm">
                                            {searchQuery 
                                                ? "Try adjusting your search query or filters to find more results" 
                                                : "Use the search box above or apply filters on the left to discover companies"
                                            }
                                        </p>
                                        {!searchQuery && (
                                            <Button 
                                                onClick={() => {
                                                    const domain = params.domain;
                                                    router.push(`/${domain}/lead-generation/companies/find-leads`);
                                                }}
                                                size="sm" 
                                                className="flex items-center space-x-2 mx-auto"
                                            >
                                                <MagnifyingGlassIcon className="size-4" />
                                                <span>Find Companies</span>
                                            </Button>
                                        )}
                                    </>
                                )}
                            </div>
                        </div>
                    ) : (
                        /* Table with Results */
                        <ScrollArea className="size-full scrollBlockChild">
                            <Table>
                                <TableHeader>
                                    <TableRow className="border-b border-neutral-200 bg-white sticky top-0 z-10">
                                        <TableHead className="w-12 h-10 px-3">
                                            <Checkbox checked={selectedLeads.length === filteredLeads.length && filteredLeads.length > 0} onCheckedChange={handleSelectAll} />
                                        </TableHead>
                                        <TableHead className="h-10 px-1 text-left font-bold text-black">Name</TableHead>
                                        <TableHead className="h-10 px-1 text-left font-bold text-black">Industry</TableHead>
                                        <TableHead className="h-10 px-1 text-left font-bold text-black">Location</TableHead>
                                        <TableHead className="h-10 px-1 text-left font-bold text-black">Email</TableHead>
                                        <TableHead className="h-10 px-1 text-left font-bold text-black">Phone number</TableHead>
                                        <TableHead className="h-10 px-1 text-left font-bold text-black">Social Links</TableHead>
                                        <TableHead className="w-12 h-10 px-1"></TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredLeads.map((lead) => {
                                        // Type guard to ensure we're working with company data
                                        const isCompanyLead = lead?.type === 'company';
                                        const apolloCompanyData = isCompanyLead ? (lead.apolloData as any) : null;
                                        
                                        return (
                                        <TableRow key={lead.id} className="border-b border-neutral-200 hover:bg-neutral-50 transition-colors group">
                                            <TableCell className="w-12 px-3 relative">
                                                <Checkbox checked={selectedLeads.includes(lead.id)} onCheckedChange={(checked: boolean) => handleSelectLead(lead.id, checked)} className={`${selectedLeads.includes(lead.id) ? 'opacity-100 visible' : 'invisible opacity-0 group-hover:opacity-100 group-hover:visible'}`} />
                                            </TableCell>
                                            <TableCell className="px-1">
                                                <button 
                                                    className="text-primary hover:text-primary/80 font-semibold text-xs cursor-pointer hover:border-b hover:border-neutral-600 bg-transparent border-none p-0" 
                                                    onClick={() => handleNameClick(lead)}
                                                >
                                                    {lead.name}
                                                </button>
                                            </TableCell>
                                            <TableCell className="px-1 text-xs text-muted-foreground">{String(apolloCompanyData?.industry || '-')}</TableCell>
                                            <TableCell className="px-1 text-xs text-muted-foreground">
                                                {[apolloCompanyData?.city, apolloCompanyData?.state, apolloCompanyData?.country].filter(Boolean).join(', ') || '-'}
                                            </TableCell>
                                            <TableCell className="px-1">
                                                <ActionButton icon={EnvelopeIcon} onClick={() => handleUnlockEmail(lead.id)}>Unlock Email</ActionButton>
                                            </TableCell>
                                            <TableCell className="px-1">
                                                <ActionButton icon={PhoneIcon} onClick={() => handleUnlockPhone(lead.id)}>Unlock Mobile</ActionButton>
                                            </TableCell>
                                            <TableCell className="px-1">
                                                <ViewLinksModal trigger={<button className="text-primary hover:text-primary/80 font-semibold text-xs flex items-center gap-1 cursor-pointer bg-transparent border-none p-0">View links<ChevronRightIcon className="size-3" /></button>} links={getContactLinks(lead)} />
                                            </TableCell>
                                            <TableCell className="w-12 px-2 relative">
                                                <LeadActionsDropdown trigger={<Button variant="ghost" size="sm" className="h-7 w-7 p-0 text-neutral-400 hover:text-neutral-600 invisible opacity-0 group-hover:opacity-100 group-hover:visible"><EllipsisVerticalIcon className="size-4" /></Button>} onSendEmail={() => {}} onAddToSegments={() => {}} onAddToDatabase={() => {}} onAddToWorkflow={() => {}} />
                                            </TableCell>
                                        </TableRow>
                                        )
                                    })}
                                </TableBody>
                            </Table>
                            <div className="border-b border-neutral-200"></div>
                        </ScrollArea>
                    )}
                </div>
            </div>
        
            <UnlockEmailModal open={emailModalOpen} onOpenChange={setEmailModalOpen} leadId={selectedLeadId} />
            <UnlockPhoneModal open={phoneModalOpen} onOpenChange={setPhoneModalOpen} leadId={selectedLeadId} />
        </>
    )
}

export default MyLeads
