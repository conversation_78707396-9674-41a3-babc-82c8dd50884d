import { getRepository } from '../connection/db';
import { BaseService } from './service';
import { Repository } from 'typeorm/repository/Repository';
import { ServerProcessingError, UniqueConstraintError } from "../errors/AppError";
import { Lead, LeadType, LeadSource } from "../entity/Lead";
import { In } from "typeorm";

export interface CreateLeadData extends Pick<Lead, 'workspaceId' | 'apolloId' | 'type' | 'source' | 'apolloData' | 'normalizedData' | 'searchHashes' | 'email' | 'name' | 'companyDomain' | 'createdById'> {
    meta?: Record<string, any>;
}

export interface UpdateLeadData extends Partial<Pick<Lead, 'apolloData' | 'normalizedData' | 'searchHashes' | 'email' | 'name' | 'companyDomain' | 'isUnlocked' | 'lastEnrichedAt' | 'updatedById'>> {
    meta?: Record<string, any>;
}

export class LeadService extends BaseService<Lead> {

    initRepository = (): Repository<Lead> => {
        return getRepository(Lead);
    }

    createLead = async (data: CreateLeadData): Promise<Lead> => {
        try {
            return await this.insert(data);
        } catch (err) {
            if (err.message.includes("Duplicate entry")) {
                throw new UniqueConstraintError('lead with apolloId already exists in workspace');
            }
            throw new ServerProcessingError(err.message);
        }
    }

    findByWorkspaceAndApolloId = async (workspaceId: string, apolloId: string): Promise<Lead | null> => {
        return await this.findOne({ workspaceId, apolloId });
    }

    findByWorkspaceAndIds = async (workspaceId: string, leadIds: string[]): Promise<Lead[]> => {
        return await this.find({ 
            workspaceId, 
            id: In(leadIds) 
        });
    }

    updateSearchHashes = async (leadId: string, searchHashes: string[]): Promise<void> => {
        await this.update({ id: leadId }, { searchHashes });
    }

    markAsUnlocked = async (leadId: string, apolloData?: any): Promise<void> => {
        console.log('🔍 [LEAD SERVICE] ==========================================');
        console.log('🔍 [LEAD SERVICE] 🔄 MARKING LEAD AS UNLOCKED');
        console.log('🔍 [LEAD SERVICE] ==========================================');
        console.log('🔍 [LEAD SERVICE] Lead ID:', leadId);
        console.log('🔍 [LEAD SERVICE] Apollo Data Provided:', !!apolloData);
        
        const updateData: UpdateLeadData = { 
            isUnlocked: true, 
            lastEnrichedAt: new Date() 
        };
        
        if (apolloData) {
            console.log('🔍 [LEAD SERVICE] 📊 APOLLO DATA TO BE SAVED:');
            console.log('🔍 [LEAD SERVICE] - Data Type:', typeof apolloData);
            console.log('🔍 [LEAD SERVICE] - Data Keys:', Object.keys(apolloData));
            console.log('🔍 [LEAD SERVICE] - Data Sample:', JSON.stringify(apolloData, null, 2).substring(0, 500) + '...');
            
            updateData.apolloData = apolloData;
        } else {
            console.log('🔍 [LEAD SERVICE] ℹ️ No Apollo data provided - only marking as unlocked');
        }
        
        console.log('🔍 [LEAD SERVICE] 📝 FINAL UPDATE DATA:');
        console.log('🔍 [LEAD SERVICE] - Update Data Keys:', Object.keys(updateData));
        console.log('🔍 [LEAD SERVICE] - isUnlocked:', updateData.isUnlocked);
        console.log('🔍 [LEAD SERVICE] - lastEnrichedAt:', updateData.lastEnrichedAt);
        console.log('🔍 [LEAD SERVICE] - apolloData Included:', !!updateData.apolloData);
        
        console.log('🔍 [LEAD SERVICE] 💾 SAVING TO DATABASE...');
        await this.update({ id: leadId }, updateData);
        console.log('🔍 [LEAD SERVICE] ✅ DATABASE UPDATE COMPLETED');
        console.log('🔍 [LEAD SERVICE] ==========================================');
    }

    findUnlockedLeadsForUser = async (workspaceId: string, unlockedLeadIds: string[]): Promise<Lead[]> => {
        if (unlockedLeadIds.length === 0) return [];
        
        return await this.find({
            workspaceId,
            id: In(unlockedLeadIds)
        });
    }

    searchLeadsByQuery = async (workspaceId: string, searchQuery: string, limit: number = 50, offset: number = 0): Promise<{ leads: Lead[], total: number }> => {
        const queryBuilder = this.getRepository()
            .createQueryBuilder('lead')
            .where('lead.workspaceId = :workspaceId', { workspaceId });

        if (searchQuery) {
            queryBuilder.andWhere(
                '(lead.name LIKE :search OR lead.email LIKE :search OR lead.companyDomain LIKE :search)',
                { search: `%${searchQuery}%` }
            );
        }

        const total = await queryBuilder.getCount();
        const leads = await queryBuilder
            .orderBy('lead.createdAt', 'DESC')
            .skip(offset)
            .take(limit)
            .getMany();

        return { leads, total };
    }

    // ✅ ADDING MISSING CRITICAL METHODS

    findByWorkspaceAndType = async (workspaceId: string, type: LeadType): Promise<Lead[]> => {
        return await this.find({ workspaceId, type });
    }

    findByWorkspaceAndSearchHashes = async (workspaceId: string, searchHashes: string[]): Promise<Lead[]> => {
        if (searchHashes.length === 0) return [];
        
        return await this.find({ 
            workspaceId, 
            searchHashes: In(searchHashes) 
        });
    }

    updateLead = async (leadId: string, updateData: UpdateLeadData): Promise<boolean> => {
        return await this.update({ id: leadId }, updateData);
    }

    removeLead = async (leadId: string): Promise<boolean> => {
        return await this.remove({ id: leadId });
    }

    findByWorkspaceAndFilters = async (workspaceId: string, filters: Record<string, any>): Promise<Lead[]> => {
        const queryBuilder = this.getRepository()
            .createQueryBuilder('lead')
            .where('lead.workspaceId = :workspaceId', { workspaceId });

        // Apply filters dynamically
        Object.keys(filters).forEach((key, index) => {
            if (filters[key] !== undefined && filters[key] !== null) {
                if (Array.isArray(filters[key])) {
                    queryBuilder.andWhere(`lead.${key} IN (:...${key})`, { [key]: filters[key] });
                } else {
                    queryBuilder.andWhere(`lead.${key} = :${key}`, { [key]: filters[key] });
                }
            }
        });

        return await queryBuilder.getMany();
    }
}
