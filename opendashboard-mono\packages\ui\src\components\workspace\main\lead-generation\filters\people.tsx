"use client"

import React, { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, PopoverTrigger } from "@ui/components/ui/popover"
import { But<PERSON> } from "@ui/components/ui/button"
import { Input } from "@ui/components/ui/input"
import { <PERSON>rollArea } from "@ui/components/ui/scroll-area"
import { ChevronDownIcon, ChevronUpIcon } from "@ui/components/icons/FontAwesomeRegular"
import { MagnifyingGlassIcon, FilterListIcon, PlusIcon, XmarkIcon, UserIcon, BuildingIcon, SidebarIcon } from "@ui/components/icons/FontAwesomeRegular"
import { cn } from "@ui/lib/utils"
import { SearchFilters } from "@ui/typings/lead"

// People-specific filter data
const PEOPLE_FILTER_SECTIONS = [
  {
    id: "person",
    title: "People filters",
    isExpanded: true,
    subsections: [
      {
        id: "job-titles",
        title: "Job titles",
        isExpanded: false,
        selectedItems: [] as string[],
        suggestions: [
          "Frontend Developer", "Backend Developer", "Full Stack Developer",
          "Mobile Developer", "DevOps Engineer", "QA Engineer",
          "Machine Learning Engineer", "AI Researcher",
  "Solutions Architect", "Cloud Engineer", "Cybersecurity Specialist",
          "Business Analyst", "Systems Analyst", "Project Manager",
          "Program Manager", "Operations Manager", "HR Manager", "Recruiter",
          "Finance Manager", "Accountant", "Financial Analyst",
          "UX Designer", "UI Designer", "UX Researcher", "Graphic Designer",
          "Content Strategist", "Copywriter", "Marketing Specialist",
  "Sales Executive", "Account Executive", "Customer Success Manager",
  "Chief Marketing Officer (CMO)", "Chief Operating Officer (COO)",
          "Chief Financial Officer (CFO)", "Chief Information Officer (CIO)"
        ]
      },
      {
        id: "seniority",
        title: "Seniority Level",
        isExpanded: false,
        selectedItems: [] as string[],
        options: [
          { label: "Owner", isSelected: false },
          { label: "Founder", isSelected: false },
          { label: "C-Suite", isSelected: false },
          { label: "Partner", isSelected: false },
          { label: "VP", isSelected: false },
          { label: "Head", isSelected: false },
          { label: "Director", isSelected: false },
          { label: "Manager", isSelected: false },
          { label: "Senior", isSelected: false },
          { label: "Entry", isSelected: false },
          { label: "Intern", isSelected: false }
        ]
      },
      {
        id: "location",
        title: "Personal Location",
        isExpanded: false,
        selectedItems: [] as string[],
        suggestions: [
          "United States", "California", "New York", "Texas", "Florida", "Illinois",
          "Canada", "United Kingdom", "Germany", "France", "Australia", "India",
          "San Francisco", "New York City", "Los Angeles", "Chicago", "Boston",
          "Nigeria", "Lagos", "Abuja"
        ]
      },
      {
        id: "organization-location",
        title: "Company HQ Location",
        isExpanded: false,
        selectedItems: [] as string[],
        suggestions: [
          "United States", "California", "New York", "Texas", "Florida", "Illinois",
          "Canada", "United Kingdom", "Germany", "France", "Australia", "India",
          "San Francisco", "New York City", "Los Angeles", "Chicago", "Boston",
          "Nigeria", "Lagos", "Abuja"
        ]
      },
      {
        id: "keywords",
        title: "Keywords",
        isExpanded: false,
        selectedItems: [] as string[],
        suggestions: [
          "AI", "Machine Learning", "Fintech", "SaaS", "E-commerce", "Healthcare",
          "Technology", "Software", "Digital", "Innovation", "Startup",
          "Enterprise", "Remote", "Hybrid", "Cloud", "Cybersecurity", "Data Science"
        ]
      },
      {
        id: "company-size",
        title: "Company Size",
        isExpanded: false,
        selectedItems: [] as string[],
        suggestions: [
          "1-10", "11-50", "51-200", "201-500",
          "501-1000", "1001-5000", "5001-10000", "10000+"
        ]
      },
      {
        id: "technologies",
        title: "Company Technologies",
        isExpanded: false,
        selectedItems: [] as string[],
        suggestions: [
          "salesforce", "hubspot", "aws", "google_analytics", "microsoft_azure",
          "react", "angular", "vue_js", "node_js", "python", "java", "docker",
          "kubernetes", "mongodb", "postgresql", "redis", "elasticsearch"
        ]
      },
      {
        id: "email-status",
        title: "Email Status",
        isExpanded: false,
        selectedItems: [] as string[],
        options: [
          { label: "Verified", isSelected: false },
          { label: "Unverified", isSelected: false },
          { label: "Likely to Engage", isSelected: false },
          { label: "Unavailable", isSelected: false }
        ]
      }
    ]
  }
]


// People-specific signals
const PEOPLE_SIGNALS = [
  {
    id: "recently-promoted",
    label: "Recently promoted",
    isSelected: false,
    icon: "person" as const
  },
  {
    id: "former-champion-changed-jobs",
    label: "Former champion changed jobs",
    isSelected: false,
    icon: "person" as const
  },
  {
    id: "high-buying-intent",
    label: "High buying intent",
    isSelected: false,
    icon: "person" as const
  },
  {
    id: "opened-emails",
    label: "Opened 2+ emails in past week",
    isSelected: false,
    icon: "person" as const
  },
  {
    id: "new-role",
    label: "New role",
    isSelected: false,
    icon: "person" as const
  },
  {
    id: "job-changes",
    label: "Job changes",
    isSelected: false,
    icon: "person" as const
  }
]

interface FilterSection {
  id: string
  title: string
  isExpanded: boolean
  subsections: FilterSubsection[]
}

interface FilterSubsection {
  id: string
  title: string
  isExpanded: boolean
  selectedItems?: string[]
  suggestions?: string[]
  options?: FilterOption[]
  searchPlaceholder?: string
  icon?: "person" | "building"
}

interface FilterOption {
  label: string
  isSelected: boolean
  count?: number
}

interface PeopleFilterProps {
  trigger?: React.ReactNode
  onFilterChange?: (filters: SearchFilters) => void
  onSidebarToggle?: () => void
  isSidebarMode?: boolean
  onClose?: () => void
  forceSidebar?: boolean
  excludeMyLeads?: boolean
  onExcludeMyLeadsChange?: (value: boolean) => void
}

export const PeopleFilter = ({ 
  trigger, 
  onFilterChange, 
  onSidebarToggle, 
  isSidebarMode = false, 
  onClose, 
  forceSidebar = false,
  excludeMyLeads: controlledExcludeMyLeads,
  onExcludeMyLeadsChange
}: PeopleFilterProps) => {
  
  const [sections, setSections] = useState<FilterSection[]>([
    ...PEOPLE_FILTER_SECTIONS,
    {
      id: "signals",
      title: "Signals",
      isExpanded: true,
      subsections: []
    }
  ])
  
  const [searchQueries, setSearchQueries] = useState<Record<string, string>>({})
  const [excludeMyLeads, setExcludeMyLeads] = useState(controlledExcludeMyLeads || false)
  const [signalsSearchQuery, setSignalsSearchQuery] = useState("")
  const [signals, setSignals] = useState(PEOPLE_SIGNALS)

  // Sync excludeMyLeads with parent component
  React.useEffect(() => {
    if (onFilterChange && controlledExcludeMyLeads === undefined) {
      // Only call onFilterChange if this is not a controlled component
      // For controlled components, the parent handles the state
      const currentFilters = {
        person: getCurrentPersonFilters(),
        company: getCurrentCompanyFilters(),
        signals: getCurrentSignals(),
        customFilters: getCurrentCustomFilters()
        // excludeMyLeads intentionally excluded - it's handled separately
      }
      onFilterChange(currentFilters)
    }
  }, [excludeMyLeads, controlledExcludeMyLeads])

  // Ensure onFilterChange is called when component first mounts
  React.useEffect(() => {
    if (onFilterChange) {
      const currentFilters = {
        person: getCurrentPersonFilters(),
        company: getCurrentCompanyFilters(),
        signals: getCurrentSignals(),
        customFilters: getCurrentCustomFilters()
        // excludeMyLeads intentionally excluded - it's handled separately
      }
      onFilterChange(currentFilters)
    }
  }, []) // Empty dependency array - only run once on mount

  // Update local state when controlled value changes
  React.useEffect(() => {
    if (controlledExcludeMyLeads !== undefined) {
      setExcludeMyLeads(controlledExcludeMyLeads)
    }
  }, [controlledExcludeMyLeads])

  const toggleSection = (sectionId: string) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? { ...section, isExpanded: !section.isExpanded }
        : section
    ))
  }

  const toggleSubsection = (sectionId: string, subsectionId: string) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? {
            ...section,
            subsections: section.subsections.map(subsection =>
              subsection.id === subsectionId
                ? { ...subsection, isExpanded: !subsection.isExpanded }
                : subsection
            )
          }
        : section
    ))
  }

  const handleItemSelect = (sectionId: string, subsectionId: string, item: string) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? {
            ...section,
            subsections: section.subsections.map(subsection =>
              subsection.id === subsectionId
                ? {
                    ...subsection,
                    selectedItems: subsection.selectedItems?.includes(item)
                      ? subsection.selectedItems.filter(i => i !== item)
                      : [...(subsection.selectedItems || []), item]
                  }
                : subsection
            )
          }
        : section
    ))
    // Remove setTimeout - it causes timing issues with state updates
    // setTimeout(updateFilters, 0)
  }

  const handleOptionToggle = (sectionId: string, subsectionId: string, optionLabel: string) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? {
            ...section,
            subsections: section.subsections.map(subsection =>
              subsection.id === subsectionId
                ? {
                    ...subsection,
                    options: subsection.options?.map(option =>
                      option.label === optionLabel
                        ? { ...option, isSelected: !option.isSelected }
                        : option
                    )
                  }
                : subsection
            )
          }
        : section
    ))
    // Remove setTimeout - it causes timing issues with state updates
    // setTimeout(updateFilters, 0)
  }

  const handleSearchChange = (subsectionId: string, value: string) => {
    setSearchQueries(prev => ({
      ...prev,
      [subsectionId]: value
    }))
  }

  const getFilteredSuggestions = (subsection: FilterSubsection) => {
    const query = searchQueries[subsection.id] || ""
    if (!query.trim()) return subsection.suggestions || []
    return (subsection.suggestions || []).filter(item =>
      item.toLowerCase().includes(query.toLowerCase())
    )
  }

  const getSelectedCount = (subsection: FilterSubsection) => {
    const selectedItemsCount = subsection.selectedItems?.length || 0
    const selectedOptionsCount = subsection.options?.filter(option => option.isSelected).length || 0
    
    if (subsection.suggestions && subsection.suggestions.length > 0) {
      return selectedItemsCount
    } else if (subsection.options && subsection.options.length > 0) {
      return selectedOptionsCount
    }
    
    return selectedItemsCount + selectedOptionsCount
  }

  const resetFilters = () => {
    setSections(prev => prev.map(section => ({
      ...section,
      subsections: section.subsections.map(subsection => ({
        ...subsection,
        selectedItems: [],
        options: subsection.options?.map(option => ({
          ...option,
          isSelected: false
        }))
      }))
    })))
    setSearchQueries({})
    setSignals(prev => prev.map(signal => ({ ...signal, isSelected: false })))
    setSignalsSearchQuery("")
    // Don't reset excludeMyLeads - preserve user's choice
    // setExcludeMyLeads(false)
    setTimeout(updateFilters, 0)
  }

  const handleSignalToggle = (signalId: string) => {
    setSignals(prev => prev.map(signal =>
      signal.id === signalId
        ? { ...signal, isSelected: !signal.isSelected }
        : signal
    ))
    setTimeout(updateFilters, 0)
  }

  const getFilteredSignals = () => {
    if (!signalsSearchQuery.trim()) return signals
    return signals.filter(signal =>
      signal.label.toLowerCase().includes(signalsSearchQuery.toLowerCase())
    )
  }

  const getSignalsSelectedCount = () => {
    return signals.filter(signal => signal.isSelected).length
  }

  const getIcon = (iconType: "person" | "building") => {
    return iconType === "person" ? <UserIcon className="size-3" /> : <BuildingIcon className="size-3" />
  }

  // Helper function to get all selected values from a subsection
  const getSubsectionSelectedValues = (subsection: FilterSubsection): string[] => {
    const selectedFromSuggestions = subsection.selectedItems || []
    const selectedFromOptions = subsection.options?.filter(opt => opt.isSelected).map(opt => opt.label) || []
    
    // Combine both sources and remove duplicates
    const allSelected = [...selectedFromSuggestions, ...selectedFromOptions]
    return [...new Set(allSelected)] // Remove duplicates
  }

  // Helper functions to get current filter states without clearing them
  const getCurrentPersonFilters = () => {
    const personSection = sections.find(s => s.id === "person")
    if (!personSection) return {}
    
    const filters: any = {}
    
    // Job titles
    const jobTitlesSubsection = personSection.subsections.find(s => s.id === "job-titles")
    if (jobTitlesSubsection) {
      const selectedValues = getSubsectionSelectedValues(jobTitlesSubsection)
      if (selectedValues.length > 0) {
        filters.jobTitles = selectedValues
      }
    }
    
    // Seniority level
    const senioritySubsection = personSection.subsections.find(s => s.id === "seniority")
    if (senioritySubsection) {
      const selectedValues = getSubsectionSelectedValues(senioritySubsection)
      if (selectedValues.length > 0) {
        filters.seniority = selectedValues
      }
    }
    
    // Location (personal)
    const locationSubsection = personSection.subsections.find(s => s.id === "location")
    if (locationSubsection) {
      const selectedValues = getSubsectionSelectedValues(locationSubsection)
      if (selectedValues.length > 0) {
        filters.location = selectedValues
      }
    }
    
    // Organization location (company HQ)
    const organizationLocationSubsection = personSection.subsections.find(s => s.id === "organization-location")
    if (organizationLocationSubsection) {
      const selectedValues = getSubsectionSelectedValues(organizationLocationSubsection)
      if (selectedValues.length > 0) {
        filters.organizationLocation = selectedValues
      }
    }
    
    // Keywords
    const keywordsSubsection = personSection.subsections.find(s => s.id === "keywords")
    if (keywordsSubsection) {
      const selectedValues = getSubsectionSelectedValues(keywordsSubsection)
      if (selectedValues.length > 0) {
        filters.keywords = selectedValues
      }
    }
    
    return filters
  }

  const getCurrentCompanyFilters = () => {
    // For now, return empty object since this is PeopleFilter
    // Company filters would be in a separate CompanyFilter component
    return {}
  }

  const getCurrentSignals = () => {
    const selectedSignals = signals.filter(s => s.isSelected)
    if (selectedSignals.length === 0) return {}
    
    const filters: any = {}
    selectedSignals.forEach(signal => {
      switch (signal.id) {
        case "recently-promoted":
          filters.recentlyPromoted = true
          break
        case "former-champion-changed-jobs":
          filters.formerChampionChangedJobs = true
          break
        case "high-buying-intent":
          filters.highBuyingIntent = true
          break
        case "opened-emails":
          filters.openedEmails = true
          break
        case "new-role":
          filters.newRole = true
          break
        case "job-changes":
          filters.jobChanges = true
          break
      }
    })
    
    return filters
  }

  const getCurrentCustomFilters = () => {
    const personSection = sections.find(s => s.id === "person")
    if (!personSection) return {}
    
    const filters: any = {}
    
    // Company size
    const companySizeSubsection = personSection.subsections.find(s => s.id === "company-size")
    if (companySizeSubsection) {
      const selectedValues = getSubsectionSelectedValues(companySizeSubsection)
      if (selectedValues.length > 0) {
        filters.companySize = selectedValues
      }
    }
    
    // Technologies
    const technologiesSubsection = personSection.subsections.find(s => s.id === "technologies")
    if (technologiesSubsection) {
      const selectedValues = getSubsectionSelectedValues(technologiesSubsection)
      if (selectedValues.length > 0) {
        filters.technologies = selectedValues
      }
    }
    
    // Email status
    const emailStatusSubsection = personSection.subsections.find(s => s.id === "email-status")
    if (emailStatusSubsection) {
      const selectedValues = getSubsectionSelectedValues(emailStatusSubsection)
      if (selectedValues.length > 0) {
        filters.emailStatus = selectedValues
      }
    }
    
    return filters
  }

  const updateFilters = () => {
    console.log(`🔍 [FILTER DEBUG] updateFilters called`);
    console.log(`🔍 [FILTER DEBUG] Current excludeMyLeads: ${excludeMyLeads}`);
    
    if (!onFilterChange) {
      console.log(`🔍 [FILTER DEBUG] ❌ No onFilterChange callback provided`);
      return
    }

    // Get current filters from current state to preserve existing selections
    // excludeMyLeads is NOT part of search filters - it's a display option
    const currentFilters = {
      person: getCurrentPersonFilters(),
      company: getCurrentCompanyFilters(),
      signals: getCurrentSignals(),
      customFilters: getCurrentCustomFilters()
      // excludeMyLeads intentionally excluded - it's handled separately
    }

    console.log(`🔍 [FILTER DEBUG] Building filters from current state:`, JSON.stringify(currentFilters, null, 2));
    console.log(`🔍 [FILTER DEBUG] excludeMyLeads (separate): ${excludeMyLeads}`);
    console.log(`🔒 [FRONTEND DEBUG] Filter update summary:`);
    console.log(`🔒 [FRONTEND DEBUG] - Search filters: ${JSON.stringify(currentFilters, null, 2)}`);
    console.log(`🔒 [FRONTEND DEBUG] - excludeMyLeads value: ${excludeMyLeads}`);
    console.log(`🔒 [FRONTEND DEBUG] - excludeMyLeads is separate from search filters`);
    console.log(`🔒 [FRONTEND DEBUG] ==========================================`);

    // No need to process filters again - currentFilters already has everything
    // Just send the current state to the parent
    console.log(`🔍 [FILTER DEBUG] Final filters before calling onFilterChange:`, JSON.stringify(currentFilters, null, 2));
    console.log(`🔍 [FILTER DEBUG] Calling onFilterChange with filters...`);
    
    onFilterChange(currentFilters)
    
    console.log(`🔍 [FILTER DEBUG] onFilterChange called successfully`);
  }

  // Watch for changes in sections and update filters automatically
  React.useEffect(() => {
    if (onFilterChange) {
      updateFilters()
    }
  }, [sections, signals]) // Watch for changes in these states (excludeMyLeads removed)

  // Filter content component
  const FilterContent = () => (
    <>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-neutral-200">
        <div className="flex items-center gap-2">
          <h3 className="text-sm font-semibold text-neutral-900">People Filter</h3>
        </div>
        {forceSidebar ? null : (
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-6 w-6 p-0 text-neutral-400 hover:text-neutral-600"
            onClick={isSidebarMode ? onClose : onSidebarToggle}
          >
            {isSidebarMode ? <XmarkIcon className="size-3" /> : <SidebarIcon className="size-3" />}
          </Button>
        )}
      </div>

      {/* Exclude My Leads Checkbox */}
      <div className="p-3 border-b border-neutral-200">
        <label className="flex items-center gap-2 cursor-pointer hover:bg-neutral-50 p-1 rounded">
          <input
            type="checkbox"
            checked={excludeMyLeads}
            onChange={(e) => {
              const newValue = e.target.checked
              console.log(`🔒 [FRONTEND DEBUG] excludeMyLeads checkbox changed:`);
              console.log(`🔒 [FRONTEND DEBUG] - Previous value: ${excludeMyLeads}`);
              console.log(`🔒 [FRONTEND DEBUG] - New value: ${newValue}`);
              console.log(`🔒 [FRONTEND DEBUG] ==========================================`);
              
              setExcludeMyLeads(newValue)
              if (onExcludeMyLeadsChange) {
                onExcludeMyLeadsChange(newValue)
              }
              // Don't call updateFilters here - it will clear other filters
              // The parent component will handle the state update
            }}
            className="w-3.5 h-3.5 text-neutral-900 border-neutral-300 rounded focus:ring-neutral-500"
          />
          <span className="text-xs text-neutral-700 font-medium">
            Exclude my leads
          </span>
        </label>
      </div>

      {/* Filter Sections */}
      <ScrollArea className="flex-1">
        <div className="p-3 space-y-4">
          {sections.map((section, sectionIndex) => (
            <div key={section.id} className="space-y-2">
              {/* Section Header */}
              <div 
                className="flex items-center justify-between p-2 cursor-pointer hover:bg-neutral-50 rounded border border-neutral-200"
                onClick={() => toggleSection(section.id)}
              >
                <span className="text-sm font-semibold text-neutral-900">{section.title}</span>
                <div className="flex items-center gap-2">
                  {section.id === "signals" && section.isExpanded && getSignalsSelectedCount() > 0 && (
                    <button
                      className="inline-flex items-center gap-1 px-1.5 py-0.5 text-xs rounded-full bg-neutral-100 text-neutral-700 border border-neutral-300 hover:bg-neutral-200 transition-colors"
                      onClick={(e) => {
                        e.stopPropagation()
                        setSignals(prev => prev.map(signal => ({ ...signal, isSelected: false })))
                      }}
                    >
                      {getSignalsSelectedCount()} selected
                      <XmarkIcon className="size-2" />
                    </button>
                  )}
                  {section.isExpanded ? (
                    <ChevronUpIcon className="size-3 text-neutral-500" />
                  ) : (
                    <ChevronDownIcon className="size-3 text-neutral-500" />
                  )}
                </div>
              </div>

              {/* Section Content */}
              {section.isExpanded && (
                <div className="px-3 pb-3">
                  {section.id === "signals" ? (
                    // Signals Content
                    <div>
                      {/* Search Bar */}
                      <div className="relative mb-2">
                        <MagnifyingGlassIcon className="absolute left-2.5 top-1/2 transform -translate-y-1/2 size-3 text-neutral-400" />
                        <Input
                          placeholder="Search signals..."
                          value={signalsSearchQuery}
                          onChange={(e) => setSignalsSearchQuery(e.target.value)}
                          className="pl-7 h-7 text-xs border-neutral-200 bg-neutral-50"
                        />
                      </div>

                      {/* Filter Options */}
                      <div className="space-y-1">
                        {getFilteredSignals().map((signal) => (
                          <label key={signal.id} className="flex items-center justify-between cursor-pointer hover:bg-neutral-50 p-1.5 rounded">
                            <div className="flex items-center gap-2.5">
                              <input
                                type="checkbox"
                                checked={signal.isSelected}
                                onChange={() => handleSignalToggle(signal.id)}
                                className="w-3.5 h-3.5 text-neutral-900 border-neutral-300 rounded focus:ring-neutral-500"
                              />
                              <UserIcon className="size-3 text-neutral-400" />
                              <span className="text-xs text-neutral-700">{signal.label}</span>
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>
                  ) : (
                    // Regular subsections
                    section.subsections.map((subsection) => (
                      <div key={subsection.id}>
                        {/* Subsection Header */}
                        <div 
                          className="flex items-center justify-between py-1.5 cursor-pointer"
                          onClick={() => toggleSubsection(section.id, subsection.id)}
                        >
                          <span className="text-xs font-medium text-neutral-700">{subsection.title}</span>
                          <div className="flex items-center gap-2">
                            {getSelectedCount(subsection) > 0 && (
                              <button
                                className="inline-flex items-center gap-1 px-1.5 py-0.5 text-xs rounded-full bg-neutral-100 text-neutral-700 border border-neutral-300 hover:bg-neutral-200 transition-colors"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  // Clear all selected items for this subsection
                                  setSections(prev => prev.map(s => 
                                    s.id === section.id 
                                      ? {
                                          ...s,
                                          subsections: s.subsections.map(sub => 
                                            sub.id === subsection.id
                                              ? { ...sub, selectedItems: [], options: sub.options?.map(opt => ({ ...opt, isSelected: false })) }
                                              : sub
                                          )
                                        }
                                      : s
                                  ))
                                  setTimeout(updateFilters, 0)
                                }}
                              >
                                {getSelectedCount(subsection)} selected
                                <XmarkIcon className="size-2" />
                              </button>
                            )}
                            {subsection.isExpanded ? (
                              <ChevronUpIcon className="size-2 text-neutral-500" />
                            ) : (
                              <ChevronDownIcon className="size-2 text-neutral-500" />
                            )}
                          </div>
                        </div>

                        {/* Subsection Content */}
                        {subsection.isExpanded && (
                          <div className="pl-1.5">
                            {/* Search Input */}
                            {subsection.suggestions && (
                              <div className="relative mb-2">
                                <MagnifyingGlassIcon className="absolute left-2.5 top-1/2 transform -translate-y-1/2 size-3 text-neutral-400" />
                                <Input
                                  placeholder={`Search ${subsection.title.toLowerCase()}...`}
                                  value={searchQueries[subsection.id] || ""}
                                  onChange={(e) => handleSearchChange(subsection.id, e.target.value)}
                                  className="pl-7 h-7 text-xs border-neutral-200 bg-neutral-50"
                                />
                              </div>
                            )}

                            {/* Selected Items */}
                            {subsection.selectedItems && subsection.selectedItems.length > 0 && (
                              <div className="mb-2">
                                <div className="flex flex-wrap gap-1">
                                  {subsection.selectedItems.map((item) => (
                                    <button
                                      key={item}
                                      onClick={() => handleItemSelect(section.id, subsection.id, item)}
                                      className="inline-flex items-center gap-1 px-1.5 py-0.5 text-xs rounded-full bg-neutral-900 text-white border border-neutral-900 transition-colors"
                                    >
                                      {subsection.icon === "person" && <UserIcon className="size-2" />}
                                      {item}
                                      <XmarkIcon className="size-2" />
                                    </button>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Suggestions */}
                            {subsection.suggestions && (
                              <div>
                                <div className="text-xs text-neutral-500 mb-1.5">Suggested</div>
                                <div className="flex flex-wrap gap-1">
                                  {getFilteredSuggestions(subsection).map((item) => (
                                    <button
                                      key={item}
                                      onClick={() => handleItemSelect(section.id, subsection.id, item)}
                                      className="inline-flex items-center gap-1 px-1.5 py-0.5 text-xs rounded-full bg-white text-neutral-700 border border-neutral-300 hover:border-neutral-400 transition-colors"
                                    >
                                      <PlusIcon className="size-2" />
                                      {item}
                                    </button>
                                  ))}
                                  
                                  {/* Add custom option */}
                                  {searchQueries[subsection.id] && 
                                   !getFilteredSuggestions(subsection).includes(searchQueries[subsection.id]) && (
                                    <button
                                      onClick={() => handleItemSelect(section.id, subsection.id, searchQueries[subsection.id])}
                                      className="inline-flex items-center gap-1 px-1.5 py-0.5 text-xs rounded-full bg-white text-neutral-700 border border-dashed border-neutral-300 hover:border-neutral-400 transition-colors"
                                    >
                                      <PlusIcon className="size-2" />
                                      Add "{searchQueries[subsection.id]}"
                                    </button>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Checkbox Options */}
                            {subsection.options && (
                              <div className="space-y-1.5">
                                {subsection.options.map((option) => (
                                  <label key={option.label} className="flex items-center gap-2 cursor-pointer hover:bg-neutral-50 p-1 rounded">
                                    <input
                                      type="checkbox"
                                      checked={option.isSelected}
                                      onChange={() => handleOptionToggle(section.id, subsection.id, option.label)}
                                      className="w-3.5 h-3.5 text-neutral-900 border-neutral-300 rounded focus:ring-neutral-500"
                                    />
                                    <span className="text-xs text-neutral-700">
                                      {option.label} {option.count && `(${option.count})`}
                                    </span>
                                  </label>
                                ))}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </div>
              )}

              {/* Separator */}
              {sectionIndex < sections.length - 1 && (
                <div className="border-t border-neutral-200" />
              )}
            </div>
          ))}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="p-3 border-t border-neutral-200">
        <Button
          variant="outline"
          size="sm"
          onClick={resetFilters}
          className="w-full h-7 text-xs font-medium text-neutral-700 border-neutral-300 hover:bg-neutral-50"
        >
          Reset filter
        </Button>
      </div>
    </>
  )

  // For find-leads context, always render as sidebar
  if (forceSidebar) {
    return (
      <div className="w-80 h-full bg-white border-r border-neutral-200 shadow-lg flex flex-col">
        <FilterContent />
      </div>
    )
  }

  // Sidebar mode for other components
  if (isSidebarMode) {
    return (
      <div className="w-80 h-full bg-white border-r border-neutral-200 shadow-lg flex flex-col">
        <FilterContent />
      </div>
    )
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        {trigger || (
          <Button 
            variant="ghost"
            className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium hover:bg-accent"
          >
            <FilterListIcon className="size-3"/>
            Filter
          </Button>
        )}
      </PopoverTrigger>
      <PopoverContent 
        className="w-80 p-0 rounded-none border border-neutral-200 bg-white shadow-lg"
        align="end"
        sideOffset={4}
      >
        <FilterContent />
      </PopoverContent>
    </Popover>
  )
}