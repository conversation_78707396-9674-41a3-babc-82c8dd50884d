"use client"

import React, { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, PopoverTrigger } from "@ui/components/ui/popover"
import { But<PERSON> } from "@ui/components/ui/button"
import { Input } from "@ui/components/ui/input"
import { <PERSON>rollArea } from "@ui/components/ui/scroll-area"
import { ChevronDownIcon, ChevronUpIcon } from "@ui/components/icons/FontAwesomeRegular"
import { MagnifyingGlassIcon, FilterListIcon, PlusIcon, XmarkIcon, UserIcon, BuildingIcon, SidebarIcon } from "@ui/components/icons/FontAwesomeRegular"
import { cn } from "@ui/lib/utils"
import { SearchFilters } from "@ui/typings/lead"

// Company-specific filter data
const COMPANY_FILTER_SECTIONS = [
  {
    id: "company",
    title: "Company filters",
    isExpanded: true,
    subsections: [
      {
        id: "keywords",
        title: "Company Keywords",
        isExpanded: false,
        selectedItems: [] as string[],
        suggestions: [
          "Technology", "Healthcare", "Finance", "Education", "Manufacturing",
          "Retail", "Real Estate", "Media", "Transportation", "Energy",
          "Software", "Biotechnology", "Insurance", "Consulting", "Aerospace",
          "AI", "Machine Learning", "Fintech", "SaaS", "E-commerce", "Startup"
        ]
      },
      {
        id: "company-size",
        title: "Company Size",
        isExpanded: false,
        selectedItems: [] as string[],
        suggestions: [
          "1-10", "11-50", "51-200", "201-500",
          "501-1000", "1001-5000", "5001-10000", "10000+"
        ]
      },
      {
        id: "company-location",
        title: "Company HQ Location",
        isExpanded: false,
        selectedItems: [] as string[],
        suggestions: [
          "United States", "California", "New York", "Texas", "Florida", "Illinois",
          "Canada", "United Kingdom", "Germany", "France", "Australia", "India",
          "San Francisco", "New York City", "Los Angeles", "Chicago", "Boston",
          "Seattle", "Austin", "Denver", "Toronto", "London", "Berlin"
        ]
      },
      {
        id: "exclude-company-location",
        title: "Exclude Company HQ Location",
        isExpanded: false,
        selectedItems: [] as string[],
        suggestions: [
          "Ireland", "Minnesota", "Seoul", "Tokyo", "Spain"
        ]
      },
      {
        id: "technologies",
        title: "Technologies Used",
        isExpanded: false,
        selectedItems: [] as string[],
        suggestions: [
          "salesforce", "hubspot", "aws", "google_analytics", "microsoft_azure",
          "react", "angular", "vue_js", "node_js", "python", "java", "docker",
          "kubernetes", "mongodb", "postgresql", "redis", "elasticsearch"
        ]
      },
      {
        id: "revenue-range",
        title: "Revenue Range",
        isExpanded: false,
        options: [
          { label: "Under $1M", min: 0, max: 1000000, count: 0, isSelected: false },
          { label: "$1M - $10M", min: 1000000, max: 10000000, count: 0, isSelected: false },
          { label: "$10M - $50M", min: 10000000, max: 50000000, count: 0, isSelected: false },
          { label: "$50M - $100M", min: 50000000, max: *********, count: 0, isSelected: false },
          { label: "$100M - $500M", min: *********, max: *********, count: 0, isSelected: false },
          { label: "$500M - $1B", min: *********, max: *********0, count: 0, isSelected: false },
          { label: "$1B+", min: *********0, max: null, count: 0, isSelected: false }
        ]
      },
      {
        id: "company-name",
        title: "Company Name",
        isExpanded: false,
        selectedItems: [] as string[],
        suggestions: [
          "Tech", "Digital", "Innovation", "Solutions", "Systems", "Group",
          "Corp", "Inc", "LLC", "Ltd", "Company", "Enterprises"
        ]
      },
      {
        id: "company-ids",
        title: "Apollo Company IDs",
        isExpanded: false,
        selectedItems: [] as string[]
      },
      {
        id: "latest-funding-amount",
        title: "Latest Funding Amount",
        isExpanded: false,
        options: [
          { label: "Under $1M", min: 0, max: 1000000, count: 0, isSelected: false },
          { label: "$1M - $5M", min: 1000000, max: 5000000, count: 0, isSelected: false },
          { label: "$5M - $10M", min: 5000000, max: 10000000, count: 0, isSelected: false },
          { label: "$10M - $25M", min: 10000000, max: 25000000, count: 0, isSelected: false },
          { label: "$25M - $50M", min: 25000000, max: 50000000, count: 0, isSelected: false },
          { label: "$50M+", min: 50000000, max: null, count: 0, isSelected: false }
        ]
      },
      {
        id: "total-funding",
        title: "Total Funding",
        isExpanded: false,
        options: [
          { label: "Under $5M", min: 0, max: 5000000, count: 0, isSelected: false },
          { label: "$5M - $25M", min: 5000000, max: 25000000, count: 0, isSelected: false },
          { label: "$25M - $100M", min: 25000000, max: *********, count: 0, isSelected: false },
          { label: "$100M - $500M", min: *********, max: *********, count: 0, isSelected: false },
          { label: "$500M+", min: *********, max: null, count: 0, isSelected: false }
        ]
      },
      {
        id: "latest-funding-date",
        title: "Latest Funding Date Range",
        isExpanded: false,
        selectedItems: [] as string[] // store ISO date strings (YYYY-MM-DD)
      },
      {
        id: "job-titles",
        title: "Active Job Titles",
        isExpanded: false,
        selectedItems: [] as string[]
      },
      {
        id: "job-locations",
        title: "Job Posting Locations",
        isExpanded: false,
        selectedItems: [] as string[]
      },
      {
        id: "job-count-range",
        title: "Job Posting Count Range",
        isExpanded: false,
        options: [
          { label: "1-10", min: 1, max: 10, count: 0, isSelected: false },
          { label: "11-50", min: 11, max: 50, count: 0, isSelected: false },
          { label: "51-100", min: 51, max: 100, count: 0, isSelected: false },
          { label: "100+", min: 100, max: null, count: 0, isSelected: false }
        ]
      },
      {
        id: "job-date",
        title: "Job Posting Date Range",
        isExpanded: false,
        selectedItems: [] as string[] // store ISO date strings (YYYY-MM-DD)
      }
    ]
  }
]






// Company-specific signals
const COMPANY_SIGNALS = [
  {
    id: "rapid-growth",
    label: "Rapid growth",
    isSelected: false,
    icon: "building" as const
  },
  {
    id: "company-growth",
    label: "Company growth",
    isSelected: false,
    icon: "building" as const
  },
  {
    id: "new-technologies",
    label: "New technologies",
    isSelected: false,
    icon: "building" as const
  },
  {
    id: "funding-events",
    label: "Funding events",
    isSelected: false,
    icon: "building" as const
  }
]

interface FilterSection {
  id: string
  title: string
  isExpanded: boolean
  subsections: FilterSubsection[]
}

interface FilterSubsection {
  id: string
  title: string
  isExpanded: boolean
  selectedItems?: string[]
  suggestions?: string[]
  options?: FilterOption[]
  searchPlaceholder?: string
  icon?: "person" | "building"
}

interface FilterOption {
  label: string
  isSelected: boolean
  count?: number
}

interface CompanyFilterProps {
  trigger?: React.ReactNode
  onFilterChange?: (filters: SearchFilters) => void
  onSidebarToggle?: () => void
  isSidebarMode?: boolean
  onClose?: () => void
  forceSidebar?: boolean
  excludeMyLeads?: boolean
  onExcludeMyLeadsChange?: (value: boolean) => void
}

export const CompanyFilter = ({ 
  trigger, 
  onFilterChange, 
  onSidebarToggle, 
  isSidebarMode = false, 
  onClose, 
  forceSidebar = false,
  excludeMyLeads: controlledExcludeMyLeads,
  onExcludeMyLeadsChange
}: CompanyFilterProps) => {
  
  const [sections, setSections] = useState<FilterSection[]>([
    ...COMPANY_FILTER_SECTIONS,
    {
      id: "signals",
      title: "Signals",
      isExpanded: true,
      subsections: []
    }
  ])
  
  const [searchQueries, setSearchQueries] = useState<Record<string, string>>({})
  const [excludeMyLeads, setExcludeMyLeads] = useState(controlledExcludeMyLeads || false)
  const [signalsSearchQuery, setSignalsSearchQuery] = useState("")
  const [signals, setSignals] = useState(COMPANY_SIGNALS)

  // Sync excludeMyLeads with parent component
  React.useEffect(() => {
    if (onFilterChange && controlledExcludeMyLeads === undefined) {
      // Only call onFilterChange if this is not a controlled component
      // For controlled components, the parent handles the state
      const currentFilters = {
        person: getCurrentPersonFilters(),
        company: getCurrentCompanyFilters(),
        signals: getCurrentSignals(),
        customFilters: getCurrentCustomFilters()
        // excludeMyLeads intentionally excluded - it's handled separately
      }
      onFilterChange(currentFilters)
    }
  }, [excludeMyLeads, controlledExcludeMyLeads])

  // Ensure onFilterChange is called when component first mounts
  React.useEffect(() => {
    if (onFilterChange) {
      const currentFilters = {
        person: getCurrentPersonFilters(),
        company: getCurrentCompanyFilters(),
        signals: getCurrentSignals(),
        customFilters: getCurrentCustomFilters()
        // excludeMyLeads intentionally excluded - it's handled separately
      }
      onFilterChange(currentFilters)
    }
  }, []) // Empty dependency array - only run once on mount

  // Update local state when controlled value changes
  React.useEffect(() => {
    if (controlledExcludeMyLeads !== undefined) {
      setExcludeMyLeads(controlledExcludeMyLeads)
    }
  }, [controlledExcludeMyLeads])

  const toggleSection = (sectionId: string) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? { ...section, isExpanded: !section.isExpanded }
        : section
    ))
  }

  const toggleSubsection = (sectionId: string, subsectionId: string) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? {
            ...section,
            subsections: section.subsections.map(subsection =>
              subsection.id === subsectionId
                ? { ...subsection, isExpanded: !subsection.isExpanded }
                : subsection
            )
          }
        : section
    ))
  }

  const handleItemSelect = (sectionId: string, subsectionId: string, item: string) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? {
            ...section,
            subsections: section.subsections.map(subsection =>
              subsection.id === subsectionId
                ? {
                    ...subsection,
                    selectedItems: subsection.selectedItems?.includes(item)
                      ? subsection.selectedItems.filter(i => i !== item)
                      : [...(subsection.selectedItems || []), item]
                  }
                : subsection
            )
          }
        : section
    ))
  }

  const handleOptionToggle = (sectionId: string, subsectionId: string, optionLabel: string) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? {
            ...section,
            subsections: section.subsections.map(subsection =>
              subsection.id === subsectionId
                ? {
                    ...subsection,
                    options: subsection.options?.map(option =>
                      option.label === optionLabel
                        ? { ...option, isSelected: !option.isSelected }
                        : option
                    )
                  }
                : subsection
            )
          }
        : section
    ))
  }

  const handleSearchChange = (subsectionId: string, value: string) => {
    setSearchQueries(prev => ({
      ...prev,
      [subsectionId]: value
    }))
  }

  const getFilteredSuggestions = (subsection: FilterSubsection) => {
    const query = searchQueries[subsection.id] || ""
    if (!query.trim()) return subsection.suggestions || []
    return (subsection.suggestions || []).filter(item =>
      item.toLowerCase().includes(query.toLowerCase())
    )
  }

  const getSelectedCount = (subsection: FilterSubsection) => {
    const selectedItemsCount = subsection.selectedItems?.length || 0
    const selectedOptionsCount = subsection.options?.filter(option => option.isSelected).length || 0
    
    if (subsection.suggestions && subsection.suggestions.length > 0) {
      return selectedItemsCount
    } else if (subsection.options && subsection.options.length > 0) {
      return selectedOptionsCount
    }
    
    return selectedItemsCount + selectedOptionsCount
  }

  const resetFilters = () => {
    setSections(prev => prev.map(section => ({
      ...section,
      subsections: section.subsections.map(subsection => ({
        ...subsection,
        selectedItems: [],
        options: subsection.options?.map(option => ({
          ...option,
          isSelected: false
        }))
      }))
    })))
    setSearchQueries({})
    setSignals(prev => prev.map(signal => ({ ...signal, isSelected: false })))
    setSignalsSearchQuery("")
    // Don't reset excludeMyLeads - preserve user's choice
    // setExcludeMyLeads(false)
  }

  const handleSignalToggle = (signalId: string) => {
    setSignals(prev => prev.map(signal =>
      signal.id === signalId
        ? { ...signal, isSelected: !signal.isSelected }
        : signal
    ))
  }

  const getFilteredSignals = () => {
    if (!signalsSearchQuery.trim()) return signals
    return signals.filter(signal =>
      signal.label.toLowerCase().includes(signalsSearchQuery.toLowerCase())
    )
  }

  const getSignalsSelectedCount = () => {
    return signals.filter(signal => signal.isSelected).length
  }

  const getIcon = (iconType: "person" | "building") => {
    return iconType === "person" ? <UserIcon className="size-3" /> : <BuildingIcon className="size-3" />
  }

  // Helper function to get all selected values from a subsection
  const getSubsectionSelectedValues = (subsection: FilterSubsection): string[] => {
    const selectedFromSuggestions = subsection.selectedItems || []
    const selectedFromOptions = subsection.options?.filter(opt => opt.isSelected).map(opt => opt.label) || []
    
    // Combine both sources and remove duplicates
    const allSelected = [...selectedFromSuggestions, ...selectedFromOptions]
    return [...new Set(allSelected)] // Remove duplicates
  }

  // Helper functions to get current filter states without clearing them
  const getCurrentPersonFilters = () => {
    // For now, return empty object since this is CompanyFilter
    // Person filters would be in a separate PeopleFilter component
    return {}
  }

  const getCurrentCompanyFilters = () => {
    const companySection = sections.find(s => s.id === "company")
    if (!companySection) return {}
    
    const filters: any = {}
    
    // Keywords (replaces industry)
    const keywordsSubsection = companySection.subsections.find(s => s.id === "keywords")
    if (keywordsSubsection) {
      const selectedValues = getSubsectionSelectedValues(keywordsSubsection)
      if (selectedValues.length > 0) {
        filters.keywords = selectedValues
      }
    }
    
    // Company size
    const companySizeSubsection = companySection.subsections.find(s => s.id === "company-size")
    if (companySizeSubsection) {
      const selectedValues = getSubsectionSelectedValues(companySizeSubsection)
      if (selectedValues.length > 0) {
        filters.companySize = selectedValues
      }
    }
    
    // Location
    const locationSubsection = companySection.subsections.find(s => s.id === "company-location")
    if (locationSubsection) {
      const selectedValues = getSubsectionSelectedValues(locationSubsection)
      if (selectedValues.length > 0) {
        filters.location = selectedValues
      }
    }
    
    // Technologies
    const technologiesSubsection = companySection.subsections.find(s => s.id === "technologies")
    if (technologiesSubsection) {
      const selectedValues = getSubsectionSelectedValues(technologiesSubsection)
      if (selectedValues.length > 0) {
        filters.technologies = selectedValues
      }
    }
    
    // Company name
    const companyNameSubsection = companySection.subsections.find(s => s.id === "company-name")
    if (companyNameSubsection) {
      const selectedValues = getSubsectionSelectedValues(companyNameSubsection)
      if (selectedValues.length > 0) {
        filters.name = selectedValues[0] // Company name is a single value
      }
    }
    
    // Revenue range
    const revenueRangeSubsection = companySection.subsections.find(s => s.id === "revenue-range")
    if (revenueRangeSubsection) {
      const selectedValues = getSubsectionSelectedValues(revenueRangeSubsection)
      if (selectedValues.length > 0) {
        // Parse revenue ranges to min/max values
        const revenueRanges = selectedValues.map(range => {
          if (range === "Under $1M") return { min: 0, max: 1000000 }
          if (range === "$1M - $10M") return { min: 1000000, max: 10000000 }
          if (range === "$10M - $50M") return { min: 10000000, max: 50000000 }
          if (range === "$50M - $100M") return { min: 50000000, max: ********* }
          if (range === "$100M - $500M") return { min: *********, max: ********* }
          if (range === "$500M - $1B") return { min: *********, max: *********0 }
          if (range === "$1B+") return { min: *********0, max: undefined }
          return null
        }).filter(Boolean)
        
        if (revenueRanges.length > 0) {
          filters.revenue = revenueRanges[0] // Use first selected range
        }
      }
    }
    
    // Funding amount
    const fundingAmountSubsection = companySection.subsections.find(s => s.id === "funding-amount")
    if (fundingAmountSubsection) {
      const selectedValues = getSubsectionSelectedValues(fundingAmountSubsection)
      if (selectedValues.length > 0) {
        // Parse funding ranges to min/max values
        const fundingRanges = selectedValues.map(range => {
          if (range === "Under $1M") return { min: 0, max: 1000000 }
          if (range === "$1M - $5M") return { min: 1000000, max: 5000000 }
          if (range === "$5M - $10M") return { min: 5000000, max: 10000000 }
          if (range === "$10M - $25M") return { min: 10000000, max: 25000000 }
          if (range === "$25M - $50M") return { min: 25000000, max: 50000000 }
          if (range === "$50M+") return { min: 50000000, max: undefined }
          return null
        }).filter(Boolean)
        
        if (fundingRanges.length > 0) {
          filters.fundingAmount = fundingRanges[0] // Use first selected range
        }
      }
    }
    
    // Total funding
    const totalFundingSubsection = companySection.subsections.find(s => s.id === "total-funding")
    if (totalFundingSubsection) {
      const selectedValues = getSubsectionSelectedValues(totalFundingSubsection)
      if (selectedValues.length > 0) {
        // Parse total funding ranges to min/max values
        const totalFundingRanges = selectedValues.map(range => {
          if (range === "Under $5M") return { min: 0, max: 5000000 }
          if (range === "$5M - $25M") return { min: 5000000, max: 25000000 }
          if (range === "$25M - $100M") return { min: 25000000, max: ********* }
          if (range === "$100M - $500M") return { min: *********, max: ********* }
          if (range === "$500M+") return { min: *********, max: undefined }
          return null
        }).filter(Boolean)
        
        if (totalFundingRanges.length > 0) {
          filters.totalFunding = totalFundingRanges[0] // Use first selected range
        }
      }
    }
    
    // Job postings
    const jobPostingsSubsection = companySection.subsections.find(s => s.id === "job-postings")
    if (jobPostingsSubsection) {
      const selectedValues = getSubsectionSelectedValues(jobPostingsSubsection)
      if (selectedValues.length > 0) {
        // Parse job posting ranges to min/max values
        const jobPostingRanges = selectedValues.map(range => {
          if (range === "1-10 jobs") return { min: 1, max: 10 }
          if (range === "11-50 jobs") return { min: 11, max: 50 }
          if (range === "51-100 jobs") return { min: 51, max: 100 }
          if (range === "100+ jobs") return { min: 100, max: undefined }
          return null
        }).filter(Boolean)
        
        if (jobPostingRanges.length > 0) {
          filters.numJobs = jobPostingRanges[0] // Use first selected range
        }
      }
    }
    
    return filters
  }

  const getCurrentSignals = () => {
    const selectedSignals = signals.filter(s => s.isSelected)
    if (selectedSignals.length === 0) return {}
    
    const filters: any = {}
    selectedSignals.forEach(signal => {
      switch (signal.id) {
        case "rapid-growth":
          filters.rapidGrowth = true
          break
        case "company-growth":
          filters.companyGrowth = true
          break
        case "new-technologies":
          filters.newTechnologies = true
          break
        case "funding-events":
          filters.fundingEvents = true
          break
      }
    })
    
    return filters
  }

  const getCurrentCustomFilters = () => {
    const companySection = sections.find(s => s.id === "company")
    if (!companySection) return {}
    
    const filters: any = {}
    
    // Revenue range - FIXED: Use 'revenue' to match backend interface
    const revenueRangeSubsection = companySection.subsections.find(s => s.id === "revenue-range")
    if (revenueRangeSubsection) {
      const selectedValues = getSubsectionSelectedValues(revenueRangeSubsection)
      if (selectedValues.length > 0) {
        filters.revenue = selectedValues // FIXED: Changed from 'revenueRange' to 'revenue'
      }
    }
    
    // Founded year - FIXED: Use 'foundedYear' to match backend interface
    const foundedYearSubsection = companySection.subsections.find(s => s.id === "founded-year")
    if (foundedYearSubsection) {
      const selectedValues = getSubsectionSelectedValues(foundedYearSubsection)
      if (selectedValues.length > 0) {
        filters.foundedYear = selectedValues // FIXED: Already correct
      }
    }
    
    return filters
  }

  const updateFilters = () => {
    console.log(`🔍 [FILTER DEBUG] updateFilters called`);
    console.log(`🔍 [FILTER DEBUG] Current excludeMyLeads: ${excludeMyLeads}`);
    
    if (!onFilterChange) {
      console.log(`🔍 [FILTER DEBUG] ❌ No onFilterChange callback provided`);
      return
    }

    // Get current filters from current state to preserve existing selections
    // excludeMyLeads is NOT part of search filters - it's a display option
    const currentFilters = {
      person: getCurrentPersonFilters(),
      company: getCurrentCompanyFilters(),
      signals: getCurrentSignals(),
      customFilters: getCurrentCustomFilters()
      // excludeMyLeads intentionally excluded - it's handled separately
    }

    console.log(`🔍 [FILTER DEBUG] Building filters from current state:`, JSON.stringify(currentFilters, null, 2));
    console.log(`🔍 [FILTER DEBUG] excludeMyLeads (separate): ${excludeMyLeads}`);
    console.log(`🔒 [FRONTEND DEBUG] Filter update summary:`);
    console.log(`🔒 [FRONTEND DEBUG] - Search filters: ${JSON.stringify(currentFilters, null, 2)}`);
    console.log(`🔒 [FRONTEND DEBUG] - excludeMyLeads value: ${excludeMyLeads}`);
    console.log(`🔒 [FRONTEND DEBUG] - excludeMyLeads is separate from search filters`);
    console.log(`🔒 [FRONTEND DEBUG] ==========================================`);

    // No need to process filters again - currentFilters already has everything
    // Just send the current state to the parent
    console.log(`🔍 [FILTER DEBUG] Final filters before calling onFilterChange:`, JSON.stringify(currentFilters, null, 2));
    console.log(`🔍 [FILTER DEBUG] Calling onFilterChange with filters...`);
    
    onFilterChange(currentFilters)
    
    console.log(`🔍 [FILTER DEBUG] onFilterChange called successfully`);
  }

  // Watch for changes in sections and update filters automatically
  React.useEffect(() => {
    if (onFilterChange) {
      updateFilters()
    }
  }, [sections, signals]) // Watch for changes in these states (excludeMyLeads removed)

  // Filter content component
  const FilterContent = () => (
    <>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-neutral-200">
        <div className="flex items-center gap-2">
          <h3 className="text-sm font-semibold text-neutral-900">Company Filter</h3>
        </div>
        {forceSidebar ? null : (
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-6 w-6 p-0 text-neutral-400 hover:text-neutral-600"
            onClick={isSidebarMode ? onClose : onSidebarToggle}
          >
            {isSidebarMode ? <XmarkIcon className="size-3" /> : <SidebarIcon className="size-3" />}
          </Button>
        )}
      </div>

      {/* Exclude My Leads Checkbox */}
      <div className="p-3 border-b border-neutral-200">
        <label className="flex items-center gap-2 cursor-pointer hover:bg-neutral-50 p-1 rounded">
          <input
            type="checkbox"
            checked={excludeMyLeads}
            onChange={(e) => {
              const newValue = e.target.checked
              console.log(`🔒 [FRONTEND DEBUG] excludeMyLeads checkbox changed:`);
              console.log(`🔒 [FRONTEND DEBUG] - Previous value: ${excludeMyLeads}`);
              console.log(`🔒 [FRONTEND DEBUG] - New value: ${newValue}`);
              console.log(`🔒 [FRONTEND DEBUG] ==========================================`);
              
              setExcludeMyLeads(newValue)
              if (onExcludeMyLeadsChange) {
                onExcludeMyLeadsChange(newValue)
              }
              // Don't call updateFilters here - it will clear other filters
              // The parent component will handle the state update
            }}
            className="w-3.5 h-3.5 text-neutral-900 border-neutral-300 rounded focus:ring-neutral-500"
          />
          <span className="text-xs text-neutral-700 font-medium">
            Exclude my leads
          </span>
        </label>
      </div>

      {/* Filter Sections */}
      <ScrollArea className="flex-1">
        <div className="p-3 space-y-4">
          {sections.map((section, sectionIndex) => (
            <div key={section.id} className="space-y-2">
              {/* Section Header */}
              <div 
                className="flex items-center justify-between p-2 cursor-pointer hover:bg-neutral-50 rounded border border-neutral-200"
                onClick={() => toggleSection(section.id)}
              >
                <span className="text-sm font-semibold text-neutral-900">{section.title}</span>
                <div className="flex items-center gap-2">
                  {section.id === "signals" && section.isExpanded && getSignalsSelectedCount() > 0 && (
                    <button
                      className="inline-flex items-center gap-1 px-1.5 py-0.5 text-xs rounded-full bg-neutral-100 text-neutral-700 border border-neutral-300 hover:bg-neutral-200 transition-colors"
                      onClick={(e) => {
                        e.stopPropagation()
                        setSignals(prev => prev.map(signal => ({ ...signal, isSelected: false })))
                      }}
                    >
                      {getSignalsSelectedCount()} selected
                      <XmarkIcon className="size-2" />
                    </button>
                  )}
                  {section.isExpanded ? (
                    <ChevronUpIcon className="size-3 text-neutral-500" />
                  ) : (
                    <ChevronDownIcon className="size-3 text-neutral-500" />
                  )}
                </div>
              </div>

              {/* Section Content */}
              {section.isExpanded && (
                <div className="px-3 pb-3">
                  {section.id === "signals" ? (
                    // Signals Content
                    <div>
                      {/* Search Bar */}
                      <div className="relative mb-2">
                        <MagnifyingGlassIcon className="absolute left-2.5 top-1/2 transform -translate-y-1/2 size-3 text-neutral-400" />
                        <Input
                          placeholder="Search signals..."
                          value={signalsSearchQuery}
                          onChange={(e) => setSignalsSearchQuery(e.target.value)}
                          className="pl-7 h-7 text-xs border-neutral-200 bg-neutral-50"
                        />
                      </div>

                      {/* Filter Options */}
                      <div className="space-y-1">
                        {getFilteredSignals().map((signal) => (
                          <label key={signal.id} className="flex items-center justify-between cursor-pointer hover:bg-neutral-50 p-1.5 rounded">
                            <div className="flex items-center gap-2.5">
                              <input
                                type="checkbox"
                                checked={signal.isSelected}
                                onChange={() => handleSignalToggle(signal.id)}
                                className="w-3.5 h-3.5 text-neutral-900 border-neutral-300 rounded focus:ring-neutral-500"
                              />
                              <BuildingIcon className="size-3 text-neutral-400" />
                              <span className="text-xs text-neutral-700">{signal.label}</span>
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>
                  ) : (
                    // Regular subsections
                    section.subsections.map((subsection) => (
                      <div key={subsection.id}>
                        {/* Subsection Header */}
                        <div 
                          className="flex items-center justify-between py-1.5 cursor-pointer"
                          onClick={() => toggleSubsection(section.id, subsection.id)}
                        >
                          <span className="text-xs font-medium text-neutral-700">{subsection.title}</span>
                          <div className="flex items-center gap-2">
                            {getSelectedCount(subsection) > 0 && (
                              <button
                                className="inline-flex items-center gap-1 px-1.5 py-0.5 text-xs rounded-full bg-neutral-100 text-neutral-700 border border-neutral-300 hover:bg-neutral-200 transition-colors"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  // Clear all selected items for this subsection
                                  setSections(prev => prev.map(s => 
                                    s.id === section.id 
                                      ? {
                                          ...s,
                                          subsections: s.subsections.map(sub => 
                                            sub.id === subsection.id
                                              ? { ...sub, selectedItems: [], options: sub.options?.map(opt => ({ ...opt, isSelected: false })) }
                                              : sub
                                          )
                                        }
                                      : s
                                  ))
                                  updateFilters()
                                }}
                              >
                                {getSelectedCount(subsection)} selected
                                <XmarkIcon className="size-2" />
                              </button>
                            )}
                            {subsection.isExpanded ? (
                              <ChevronUpIcon className="size-2 text-neutral-500" />
                            ) : (
                              <ChevronDownIcon className="size-2 text-neutral-500" />
                            )}
                          </div>
                        </div>

                        {/* Subsection Content */}
                        {subsection.isExpanded && (
                          <div className="pl-1.5">
                            {/* Search Input */}
                            {subsection.suggestions && (
                              <div className="relative mb-2">
                                <MagnifyingGlassIcon className="absolute left-2.5 top-1/2 transform -translate-y-1/2 size-3 text-neutral-400" />
                                <Input
                                  placeholder={`Search ${subsection.title.toLowerCase()}...`}
                                  value={searchQueries[subsection.id] || ""}
                                  onChange={(e) => handleSearchChange(subsection.id, e.target.value)}
                                  className="pl-7 h-7 text-xs border-neutral-200 bg-neutral-50"
                                />
                              </div>
                            )}

                            {/* Selected Items */}
                            {subsection.selectedItems && subsection.selectedItems.length > 0 && (
                              <div className="mb-2">
                                <div className="flex flex-wrap gap-1">
                                  {subsection.selectedItems.map((item) => (
                                    <button
                                      key={item}
                                      onClick={() => handleItemSelect(section.id, subsection.id, item)}
                                      className="inline-flex items-center gap-1 px-1.5 py-0.5 text-xs rounded-full bg-neutral-900 text-white border border-neutral-900 transition-colors"
                                    >
                                      {subsection.icon === "building" && <BuildingIcon className="size-2" />}
                                      {item}
                                      <XmarkIcon className="size-2" />
                                    </button>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Suggestions */}
                            {subsection.suggestions && (
                              <div>
                                <div className="text-xs text-neutral-500 mb-1.5">Suggested</div>
                                <div className="flex flex-wrap gap-1">
                                  {getFilteredSuggestions(subsection).map((item) => (
                                    <button
                                      key={item}
                                      onClick={() => handleItemSelect(section.id, subsection.id, item)}
                                      className="inline-flex items-center gap-1 px-1.5 py-0.5 text-xs rounded-full bg-white text-neutral-700 border border-neutral-300 hover:border-neutral-400 transition-colors"
                                    >
                                      <PlusIcon className="size-2" />
                                      {item}
                                    </button>
                                  ))}
                                  
                                  {/* Add custom option */}
                                  {searchQueries[subsection.id] && 
                                   !getFilteredSuggestions(subsection).includes(searchQueries[subsection.id]) && (
                                    <button
                                      onClick={() => handleItemSelect(section.id, subsection.id, searchQueries[subsection.id])}
                                      className="inline-flex items-center gap-1 px-1.5 py-0.5 text-xs rounded-full bg-white text-neutral-700 border border-dashed border-neutral-300 hover:border-neutral-400 transition-colors"
                                    >
                                      <PlusIcon className="size-2" />
                                      Add "{searchQueries[subsection.id]}"
                                    </button>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Checkbox Options */}
                            {subsection.options && (
                              <div className="space-y-1.5">
                                {subsection.options.map((option) => (
                                  <label key={option.label} className="flex items-center gap-2 cursor-pointer hover:bg-neutral-50 p-1 rounded">
                                    <input
                                      type="checkbox"
                                      checked={option.isSelected}
                                      onChange={() => handleOptionToggle(section.id, subsection.id, option.label)}
                                      className="w-3.5 h-3.5 text-neutral-900 border-neutral-300 rounded focus:ring-neutral-500"
                                    />
                                    <span className="text-xs text-neutral-700">
                                      {option.label} {option.count && `(${option.count})`}
                                    </span>
                                  </label>
                                ))}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </div>
              )}

              {/* Separator */}
              {sectionIndex < sections.length - 1 && (
                <div className="border-t border-neutral-200" />
              )}
            </div>
          ))}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="p-3 border-t border-neutral-200">
        <Button
          variant="outline"
          size="sm"
          onClick={resetFilters}
          className="w-full h-7 text-xs font-medium text-neutral-700 border-neutral-300 hover:bg-neutral-50"
        >
          Reset filter
        </Button>
      </div>
    </>
  )

  // For find-leads context, always render as sidebar
  if (forceSidebar) {
    return (
      <div className="w-80 h-full bg-white border-r border-neutral-200 shadow-lg flex flex-col">
        <FilterContent />
      </div>
    )
  }

  // Sidebar mode for other components
  if (isSidebarMode) {
    return (
      <div className="w-80 h-full bg-white border-r border-neutral-200 shadow-lg flex flex-col">
        <FilterContent />
      </div>
    )
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        {trigger || (
          <Button 
            variant="ghost"
            className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium hover:bg-accent"
          >
            <FilterListIcon className="size-3"/>
            Filter
          </Button>
        )}
      </PopoverTrigger>
      <PopoverContent 
        className="w-80 p-0 rounded-none border border-neutral-200 bg-white shadow-lg"
        align="end"
        sideOffset={4}
      >
        <FilterContent />
      </PopoverContent>
    </Popover>
  )
}