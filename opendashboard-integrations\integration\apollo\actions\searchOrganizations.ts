import { Action, Property, IntegrationContext } from '@opendashboard-inc/integration-core';

interface Input {
  searchParams: string;
  page?: string; 
}

interface Output {
  organizations: any[];
  totalCount: number;
  requestId: string;
  creditsUsed: number;
  processingTime: number;
}

export const searchOrganizations: Action<Input, Output> = {
  identifier: 'searchOrganizations',
  displayName: 'Search Organizations',
  description:
    'Search for companies/organizations using Apollo API parameters. Apollo caps results at 100 per page and 50,000 total per search query (500 pages max).',
  intent: 'read',

  props: {
    searchParams: Property.LongText({
      displayName: 'Search Parameters (JSON)',
      description:
        'Search filters in nested format (e.g., {"filters": {"company": {"industry": ["IT"], "companySize": ["51-200"]}}}) or flat format (e.g., {"industry": ["IT"], "companySize": ["51-200"]}). The integration will automatically transform nested format to Apollo-compatible flat format.',
      required: true,
    }),
    page: Property.ShortText({
      displayName: 'Page Number',
      description: 'Apollo page number (1, 2, 3, etc.) for pagination',
      required: false,
    }),
  },

  run: async (context: IntegrationContext<Input>): Promise<Output> => {
    const { auth, propsValue } = context;

    
    const searchParams: Record<string, any> =
      typeof propsValue.searchParams === 'string'
        ? JSON.parse(propsValue.searchParams)
        : propsValue.searchParams;

    const page = parseInt(propsValue.page || '1', 10);

    const startTime = Date.now();

    
    const filters = searchParams.filters || searchParams;
    const company = filters.company || filters;
    
    const apolloFilters: any = {};
    
    
    if (company.companySize && company.companySize.length > 0) {
        apolloFilters.organization_num_employees_ranges = company.companySize;
    }
    
    
    if (company.location && company.location.length > 0) {
        apolloFilters.organization_locations = company.location;
    }
    
    
    if (company.keywords && company.keywords.length > 0) {
        apolloFilters.q_organization_keyword_tags = company.keywords;
    }
    
    
    if (company.name) {
        apolloFilters.q_organization_name = company.name;
    }
    
    
    if (company.revenue) {
            if (company.revenue.min !== undefined) {
                apolloFilters['revenue_range[min]'] = company.revenue.min;
            }
            if (company.revenue.max !== undefined) {
                apolloFilters['revenue_range[max]'] = company.revenue.max;
            }
        }
    
    
    if (company.technologies && company.technologies.length > 0) {
        apolloFilters.currently_using_any_of_technology_uids = company.technologies;
    }
    
   
    if (company.fundingAmount) {
        if (company.fundingAmount.min !== undefined) {
            apolloFilters['latest_funding_amount_range[min]'] = company.fundingAmount.min;
        }
        if (company.fundingAmount.max !== undefined) {
            apolloFilters['latest_funding_amount_range[max]'] = company.fundingAmount.max;
        }
    }
    
    if (company.totalFunding) {
        if (company.totalFunding.min !== undefined) {
            apolloFilters['total_funding_range[min]'] = company.totalFunding.min;
        }
        if (company.totalFunding.max !== undefined) {
            apolloFilters['total_funding_range[max]'] = company.totalFunding.max;
        }
    }
    
    if (company.numJobs) {
        if (company.numJobs.min !== undefined) {
            apolloFilters['organization_num_jobs_range[min]'] = company.numJobs.min;
        }
        if (company.numJobs.max !== undefined) {
            apolloFilters['organization_num_jobs_range[max]'] = company.numJobs.max;
        }
    }
    
 
    const { api_key } = searchParams;
    
    const apolloParams = {
      per_page: 100, 
      page,
      ...apolloFilters, // Use converted filters
    };
    
    console.log('🔧 [INTEGRATION DEBUG] Final Apollo API call:', {
        url: 'https://api.apollo.io/api/v1/mixed_companies/search',
        method: 'POST',
        headers: { 'X-Api-Key': '***', 'Content-Type': 'application/json' },
        body: apolloParams
    });



    const res = await fetch('https://api.apollo.io/api/v1/mixed_companies/search', {
      method: 'POST',
      headers: {
        'X-Api-Key': auth.apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apolloParams),
    });

    
    const data = await res.json();

    if (!res.ok) {
      throw new Error(
        `Apollo API error: ${res.status} ${res.statusText} - ${JSON.stringify(data)}`
      );
    }

    const processingTime = Date.now() - startTime;

   
    const organizations = data.organizations || [];
    const totalCount = data.pagination?.total_entries || organizations.length;

    return {
      organizations,
      totalCount,
      requestId: data.request_id || data.requestId || `req_${Date.now()}`,
      creditsUsed: (data.credits_used || data.creditsUsed) ?? 0,
      processingTime,
    };
  },
};
