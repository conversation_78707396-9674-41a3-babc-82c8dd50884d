# Company Lead Data Structure

## 🚨 CRITICAL BUG IDENTIFIED AND FIXED 🚨

### **The Problem:**
Apollo company data was being returned from the API but showing as "N/A" in the UI because:

1. **Backend Entity Interfaces Missing Critical Fields** ❌
   - The `ApolloCompanyData` interface was missing growth metrics, market cap, and other fields
   - Fields like `organization_headcount_six_month_growth` were not defined in the backend
   - This caused data to be lost during database operations

2. **Data Loss During Save/Retrieve** ❌
   - Apollo returns complete data (e.g., growth metrics, market cap)
   - Backend entity couldn't store these fields due to missing interface definitions
   - Data was truncated/silently dropped

3. **UI Showing "N/A" Instead of Real Data** ❌
   - Frontend was correctly trying to display the data
   - But the data was already lost in the backend
   - `formatGrowthPercentage(null)` correctly returned "N/A"

### **The Fix Applied:**
✅ **Updated Backend Entity Interfaces** - Added all missing Apollo fields:
- Growth metrics: `organization_headcount_six_month_growth`, etc.
- Financial data: `market_cap`, `organization_revenue_printed`
- Intent signals: `show_intent`, `intent_strength`
- Company structure: `owned_by_organization`, `secondary_industries`
- Additional fields: `retail_location_count`, `industry_tag_hash`

✅ **Enhanced Frontend Display** - Added missing fields to UI:
- Market Cap display (was completely missing)
- Employee count with proper formatting
- Industry information in multiple sections
- Retail locations and secondary industries

### **Result:**
- **Before**: Growth metrics showed "N/A" for ALL companies
- **After**: Growth metrics now display actual percentages when available
- **Before**: Market cap was completely hidden
- **After**: Market cap now displays (e.g., "204.9B" for PepsiCo)

### **Why This Happened:**
The backend entity interfaces were incomplete, causing a data pipeline break:
```
Apollo API → Backend Entity (missing fields) → Database (truncated data) → Frontend (shows "N/A")
```

### **Root Cause:**
Incomplete TypeScript interfaces in the backend entity definitions, not a frontend or database issue.

---

## Purpose
This file documents the actual API response structure for company leads from the Apollo integration.
Use this to map all available data fields to the UI components in the company details page.

 "status": "ok",
    "message": "The requested action is successful",
    "data": {
        "leads": [
            {
                "id": "744393f3-7cc6-4cf6-b002-eb4bc7dd5075",
                "workspaceId": "02fd83ca-0c00-427e-8342-f0cc164b286c",
                "apolloId": "55923b847369641739148700",
                "type": "company",
                "source": "apollo",
                "apolloData": {
                    "id": "55923b847369641739148700",
                    "name": "VENTURES AFRICA",
                    "phone": "+*********** 6826",
                    "blog_url": null,
                    "logo_url": "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/6899c7001ba63a0001bb35d6/picture",
                    "languages": [
                        "English"
                    ],
                    "show_intent": true,
                    "twitter_url": null,
                    "website_url": "http://www.venturesafrica.com",
                    "facebook_url": null,
                    "founded_year": 2012,
                    "linkedin_uid": "2552019",
                    "linkedin_url": "http://www.linkedin.com/company/ventures-africa",
                    "alexa_ranking": null,
                    "angellist_url": null,
                    "primary_phone": {
                        "number": "+*********** 6826",
                        "source": "Account",
                        "sanitized_number": "+*************"
                    },
                    "crunchbase_url": null,
                    "primary_domain": "venturesafrica.com",
                    "intent_strength": null,
                    "sanitized_phone": "+*************",
                    "organization_revenue": 0,
                    "intent_signal_account": null,
                    "publicly_traded_symbol": null,
                    "owned_by_organization_id": null,
                    "publicly_traded_exchange": null,
                    "has_intent_signal_account": false,
                    "organization_revenue_printed": null,
                    "organization_headcount_six_month_growth": -0.*****************,
                    "organization_headcount_twelve_month_growth": 0.*****************,
                    "organization_headcount_twenty_four_month_growth": -0.*****************
                },
                "normalizedData": {
                    "id": "55923b847369641739148700",
                    "name": "VENTURES AFRICA",
                    "phone": "<EMAIL>",
                    "company": "VENTURES AFRICA",
                    "photoUrl": "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/6899c7001ba63a0001bb35d6/picture",
                    "linkedinUrl": "http://www.linkedin.com/company/ventures-africa",
                    "companyDomain": "venturesafrica.com",
                    "isEmailVisible": false,
                    "isPhoneVisible": false,
                    "email": "<EMAIL>"
                },
                "searchHashes": [
                    "215933d3db89fe51772a794df3624aaab7bea519beafb69da9c7ae7a9d4d919a",
                    "5bcf6b0b50c96c9248d288e1ab669f2a4e048cfa4a6bf02df4ef04ee01d20f55"
                ],
                "email": null,
                "companyDomain": "venturesafrica.com",
                "name": "VENTURES AFRICA",
                "isUnlocked": false,
                "lastEnrichedAt": null,
                "createdById": "b5b4fdda-9fb0-4433-9068-4ed11878e427",
                "updatedById": null,
                "createdAt": "2025-08-30T00:58:19.090Z",
                "updatedAt": "2025-08-30T00:58:19.090Z",
                "deletedAt": null,
                "meta": null
            },
            {
                "id": "abfd37b7-f24a-49d4-9727-1140bd407c12",
                "workspaceId": "02fd83ca-0c00-427e-8342-f0cc164b286c",
                "apolloId": "54a121df69702d97c1493603",
                "type": "company",
                "source": "apollo",
                "apolloData": {
                    "id": "54a121df69702d97c1493603",
                    "name": "UBA Group",
                    "phone": "+*********** 5822",
                    "blog_url": null,
                    "logo_url": "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/68b00946293d100001080223/picture",
                    "languages": [
                        "English",
                        "English"
                    ],
                    "show_intent": true,
                    "twitter_url": "https://twitter.com/UBAGroup",
                    "website_url": "http://www.ubagroup.com",
                    "facebook_url": "https://facebook.com/ubagroup",
                    "founded_year": 1949,
                    "linkedin_uid": "41479",
                    "linkedin_url": "http://www.linkedin.com/company/uba",
                    "alexa_ranking": 20149,
                    "angellist_url": null,
                    "primary_phone": {
                        "number": "+*********** 5822",
                        "source": "Owler",
                        "sanitized_number": "+*************"
                    },
                    "crunchbase_url": null,
                    "primary_domain": "ubagroup.com",
                    "intent_strength": null,
                    "sanitized_phone": "+*************",
                    "organization_revenue": **********,
                    "intent_signal_account": null,
                    "publicly_traded_symbol": "UBA",
                    "owned_by_organization_id": null,
                    "publicly_traded_exchange": "other",
                    "has_intent_signal_account": false,
                    "organization_revenue_printed": "2.7B",
                    "organization_headcount_six_month_growth": 0,
                    "organization_headcount_twelve_month_growth": 0.006184716877405167,
                    "organization_headcount_twenty_four_month_growth": 0.*****************
                },
                "normalizedData": {
                    "id": "54a121df69702d97c1493603",
                    "name": "UBA Group",
                    "phone": "<EMAIL>",
                    "company": "UBA Group",
                    "photoUrl": "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/68b00946293d100001080223/picture",
                    "linkedinUrl": "http://www.linkedin.com/company/uba",
                    "companyDomain": "ubagroup.com",
                    "isEmailVisible": false,
                    "isPhoneVisible": false,
                    "email": "<EMAIL>"
                },
                "searchHashes": [
                    "01c95630651e5f045458aa58e78ea301ee02c014bd2a935a83d37b3a3a0e1036",
                    "215933d3db89fe51772a794df3624aaab7bea519beafb69da9c7ae7a9d4d919a",
                    "caa66a01c732e3d06b92534cf564aefbc9c985921eda9b95cdfbfe1a7cf5c09b",
                    "5bcf6b0b50c96c9248d288e1ab669f2a4e048cfa4a6bf02df4ef04ee01d20f55"
                ],
                "email": null,
                "companyDomain": "ubagroup.com",
                "name": "UBA Group",
                "isUnlocked": false,
                "lastEnrichedAt": null,
                "createdById": "b5b4fdda-9fb0-4433-9068-4ed11878e427",
                "updatedById": null,
                "createdAt": "2025-08-29T18:00:03.181Z",
                "updatedAt": "2025-08-30T01:01:55.000Z",
                "deletedAt": null,
                "meta": null
            },
            {
                "id": "372f87b4-70fb-46cb-bb64-621473f58341",
                "workspaceId": "02fd83ca-0c00-427e-8342-f0cc164b286c",
                "apolloId": "5a9dd9e3a6da98d97e788604",
                "type": "company",
                "source": "apollo",
                "apolloData": {
                    "id": "5a9dd9e3a6da98d97e788604",
                    "name": "Jobberman Nigeria",
                    "phone": "+*********** 9999",
                    "blog_url": null,
                    "logo_url": "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/68a8486f2481250001ff8bbc/picture",
                    "languages": [
                        "English"
                    ],
                    "show_intent": true,
                    "twitter_url": "https://www.twitter.com/jobbermandotcom",
                    "website_url": "http://www.jobberman.com",
                    "facebook_url": "https://www.facebook.com/jobbermannigeria",
                    "founded_year": 2009,
                    "linkedin_uid": "691030",
                    "linkedin_url": "http://www.linkedin.com/company/jobbermannigeria",
                    "alexa_ranking": 79849,
                    "angellist_url": "http://angel.co/ngcareersltd",
                    "primary_phone": {
                        "number": "+*********** 9999",
                        "source": "Owler",
                        "sanitized_number": "+*************"
                    },
                    "crunchbase_url": null,
                    "primary_domain": "jobberman.com",
                    "intent_strength": null,
                    "sanitized_phone": "+*************",
                    "organization_revenue": 9000000,
                    "intent_signal_account": null,
                    "owned_by_organization": {
                        "id": "5a9f4780a6da98d97017a42c",
                        "name": "ROAM Africa",
                        "website_url": "http://www.roam.africa"
                    },
                    "publicly_traded_symbol": null,
                    "owned_by_organization_id": "5a9f4780a6da98d97017a42c",
                    "publicly_traded_exchange": null,
                    "has_intent_signal_account": false,
                    "organization_revenue_printed": "9M",
                    "organization_headcount_six_month_growth": 0.****************,
                    "organization_headcount_twelve_month_growth": 0.*****************,
                    "organization_headcount_twenty_four_month_growth": 0.****************
                },
                "normalizedData": {
                    "id": "5a9dd9e3a6da98d97e788604",
                    "name": "Jobberman Nigeria",
                    "phone": "<EMAIL>",
                    "company": "Jobberman Nigeria",
                    "photoUrl": "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/68a8486f2481250001ff8bbc/picture",
                    "linkedinUrl": "http://www.linkedin.com/company/jobbermannigeria",
                    "companyDomain": "jobberman.com",
                    "isEmailVisible": false,
                    "isPhoneVisible": false,
                    "email": "<EMAIL>"
                },
                "searchHashes": [
                    "215933d3db89fe51772a794df3624aaab7bea519beafb69da9c7ae7a9d4d919a",
                    "5bcf6b0b50c96c9248d288e1ab669f2a4e048cfa4a6bf02df4ef04ee01d20f55"
                ],
                "email": null,
                "companyDomain": "jobberman.com",
                "name": "Jobberman Nigeria",
                "isUnlocked": false,
                "lastEnrichedAt": null,
                "createdById": "b5b4fdda-9fb0-4433-9068-4ed11878e427",
                "updatedById": null,
                "createdAt": "2025-08-30T00:58:19.155Z",
                "updatedAt": "2025-08-30T00:58:19.155Z",
                "deletedAt": null,
                "meta": null
            },
            {
                "id": "e9de4d21-38ab-4a8c-a64b-b76cc0af6cbe",
                "workspaceId": "02fd83ca-0c00-427e-8342-f0cc164b286c",
                "apolloId": "5da4758ed7e66f0001cd098b",
                "type": "company",
                "source": "apollo",
                "apolloData": {
                    "id": "5da4758ed7e66f0001cd098b",
                    "name": "Nigeria Latest Opportunities",
                    "phone": "+234 ************",
                    "blog_url": null,
                    "logo_url": "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/68a3a882ec3c9400017230f2/picture",
                    "languages": [],
                    "show_intent": true,
                    "twitter_url": null,
                    "website_url": "http://www.latestopportunities.com.ng",
                    "facebook_url": null,
                    "founded_year": 2018,
                    "linkedin_uid": "********",
                    "linkedin_url": "http://www.linkedin.com/company/nigeria-latest-jobs",
                    "alexa_ranking": null,
                    "angellist_url": null,
                    "primary_phone": {
                        "number": "+234 ************",
                        "source": "Account",
                        "sanitized_number": "+*************"
                    },
                    "crunchbase_url": null,
                    "primary_domain": "latestopportunities.com.ng",
                    "intent_strength": null,
                    "sanitized_phone": "+*************",
                    "organization_revenue": 0,
                    "intent_signal_account": null,
                    "publicly_traded_symbol": null,
                    "owned_by_organization_id": null,
                    "publicly_traded_exchange": null,
                    "has_intent_signal_account": false,
                    "organization_revenue_printed": null,
                    "organization_headcount_six_month_growth": 0,
                    "organization_headcount_twelve_month_growth": 0.*****************,
                    "organization_headcount_twenty_four_month_growth": 0.*****************
                },
                "normalizedData": {
                    "id": "5da4758ed7e66f0001cd098b",
                    "name": "Nigeria Latest Opportunities",
                    "phone": "<EMAIL>",
                    "company": "Nigeria Latest Opportunities",
                    "photoUrl": "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/68a3a882ec3c9400017230f2/picture",
                    "linkedinUrl": "http://www.linkedin.com/company/nigeria-latest-jobs",
                    "companyDomain": "latestopportunities.com.ng",
                    "isEmailVisible": false,
                    "isPhoneVisible": false,
                    "email": "<EMAIL>"
                },
                "searchHashes": [
                    "215933d3db89fe51772a794df3624aaab7bea519beafb69da9c7ae7a9d4d919a",
                    "caa66a01c732e3d06b92534cf564aefbc9c985921eda9b95cdfbfe1a7cf5c09b",
                    "5bcf6b0b50c96c9248d288e1ab669f2a4e048cfa4a6bf02df4ef04ee01d20f55"
                ],
                "email": null,
                "companyDomain": "latestopportunities.com.ng",
                "name": "Nigeria Latest Opportunities",
                "isUnlocked": false,
                "lastEnrichedAt": null,
                "createdById": "b5b4fdda-9fb0-4433-9068-4ed11878e427",
                "updatedById": null,
                "createdAt": "2025-08-30T00:58:19.279Z",
                "updatedAt": "2025-08-30T01:01:55.000Z",
                "deletedAt": null,
                "meta": null
            },
            {
                "id": "08062678-7233-4bf7-b1ca-e356c892abe6",
                "workspaceId": "02fd83ca-0c00-427e-8342-f0cc164b286c",
                "apolloId": "57c4a78ea6da9840e5850f48",
                "type": "company",
                "source": "apollo",
                "apolloData": {
                    "id": "57c4a78ea6da9840e5850f48",
                    "name": "Interswitch Group",
                    "phone": "+234 1 628 3888",
                    "blog_url": null,
                    "logo_url": "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/68ad7716e808fb0001c9ec0c/picture",
                    "languages": [
                        "English"
                    ],
                    "show_intent": true,
                    "twitter_url": "https://twitter.com/InterswitchGRP",
                    "website_url": "http://www.interswitchgroup.com",
                    "facebook_url": "https://facebook.com/InterswitchGroup/",
                    "founded_year": 2002,
                    "linkedin_uid": "510177",
                    "linkedin_url": "http://www.linkedin.com/company/interswitch-limited",
                    "alexa_ranking": 70141,
                    "angellist_url": null,
                    "primary_phone": {
                        "number": "+234 1 628 3888",
                        "source": "Owler",
                        "sanitized_number": "+***********"
                    },
                    "crunchbase_url": null,
                    "primary_domain": "interswitchgroup.com",
                    "intent_strength": null,
                    "sanitized_phone": "+***********",
                    "organization_revenue": ********,
                    "intent_signal_account": null,
                    "publicly_traded_symbol": null,
                    "owned_by_organization_id": null,
                    "publicly_traded_exchange": null,
                    "has_intent_signal_account": false,
                    "organization_revenue_printed": "82.1M",
                    "organization_headcount_six_month_growth": 0.*****************,
                    "organization_headcount_twelve_month_growth": 0.*****************,
                    "organization_headcount_twenty_four_month_growth": 0.****************
                },
                "normalizedData": {
                    "id": "57c4a78ea6da9840e5850f48",
                    "name": "Interswitch Group",
                    "phone": "<EMAIL>",
                    "company": "Interswitch Group",
                    "photoUrl": "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/68ad7716e808fb0001c9ec0c/picture",
                    "linkedinUrl": "http://www.linkedin.com/company/interswitch-limited",
                    "companyDomain": "interswitchgroup.com",
                    "isEmailVisible": false,
                    "isPhoneVisible": false,
                    "email": "<EMAIL>"
                },
                "searchHashes": [
                    "215933d3db89fe51772a794df3624aaab7bea519beafb69da9c7ae7a9d4d919a",
                    "caa66a01c732e3d06b92534cf564aefbc9c985921eda9b95cdfbfe1a7cf5c09b",
                    "5bcf6b0b50c96c9248d288e1ab669f2a4e048cfa4a6bf02df4ef04ee01d20f55"
                ],
                "email": null,
                "companyDomain": "interswitchgroup.com",
                "name": "Interswitch Group",
                "isUnlocked": false,
                "lastEnrichedAt": null,
                "createdById": "b5b4fdda-9fb0-4433-9068-4ed11878e427",
                "updatedById": null,
                "createdAt": "2025-08-30T00:58:19.301Z",
                "updatedAt": "2025-08-30T01:01:55.000Z",
                "deletedAt": null,
                "meta": null
            },
            {
                "id": "2d9334aa-dd24-4564-a634-a983c5f9ba29",
                "workspaceId": "02fd83ca-0c00-427e-8342-f0cc164b286c",
                "apolloId": "6569a8f70592b500019ec788",
                "type": "company",
                "source": "apollo",
                "apolloData": {
                    "id": "6569a8f70592b500019ec788",
                    "name": "Nokhouse Design - UI/UX Design Agency",
                    "phone": null,
                    "blog_url": null,
                    "logo_url": "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/66eaa8457722a80001797988/picture",
                    "languages": [],
                    "show_intent": true,
                    "twitter_url": null,
                    "website_url": "http://www.nokhouse.com",
                    "facebook_url": null,
                    "founded_year": 2023,
                    "linkedin_uid": "********",
                    "linkedin_url": "http://www.linkedin.com/company/nokhouse",
                    "alexa_ranking": null,
                    "angellist_url": null,
                    "primary_phone": {},
                    "crunchbase_url": null,
                    "primary_domain": "nokhouse.com",
                    "intent_strength": null,
                    "organization_revenue": 0,
                    "intent_signal_account": null,
                    "publicly_traded_symbol": null,
                    "owned_by_organization_id": null,
                    "publicly_traded_exchange": null,
                    "has_intent_signal_account": false,
                    "organization_revenue_printed": null,
                    "organization_headcount_six_month_growth": -1,
                    "organization_headcount_twelve_month_growth": -1,
                    "organization_headcount_twenty_four_month_growth": -1
                },
                "normalizedData": {
                    "id": "6569a8f70592b500019ec788",
                    "name": "Nokhouse Design - UI/UX Design Agency",
                    "phone": "<EMAIL>",
                    "company": "Nokhouse Design - UI/UX Design Agency",
                    "photoUrl": "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/66eaa8457722a80001797988/picture",
                    "linkedinUrl": "http://www.linkedin.com/company/nokhouse",
                    "companyDomain": "nokhouse.com",
                    "isEmailVisible": false,
                    "isPhoneVisible": false,
                    "email": "<EMAIL>"
                },
                "searchHashes": [
                    "215933d3db89fe51772a794df3624aaab7bea519beafb69da9c7ae7a9d4d919a",
                    "5bcf6b0b50c96c9248d288e1ab669f2a4e048cfa4a6bf02df4ef04ee01d20f55"
                ],
                "email": null,
                "companyDomain": "nokhouse.com",
                "name": "Nokhouse Design - UI/UX Design Agency",
                "isUnlocked": false,
                "lastEnrichedAt": null,
                "createdById": "b5b4fdda-9fb0-4433-9068-4ed11878e427",
                "updatedById": null,
                "createdAt": "2025-08-30T00:58:19.196Z",
                "updatedAt": "2025-08-30T00:58:19.196Z",
                "deletedAt": null,
                "meta": null
            },
            {
                "id": "d560a53f-3e76-4842-a77e-b2fe5d12e7ad",
                "workspaceId": "02fd83ca-0c00-427e-8342-f0cc164b286c",
                "apolloId": "54a1b8e974686940128e360b",
                "type": "company",
                "source": "apollo",
                "apolloData": {
                    "id": "54a1b8e974686940128e360b",
                    "name": "MTN Nigeria",
                    "phone": "+*********** 0180",
                    "blog_url": null,
                    "logo_url": "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/68aeb55379140f0001c3441d/picture",
                    "languages": [
                        "English"
                    ],
                    "show_intent": true,
                    "twitter_url": "https://twitter.com/MTNNG",
                    "website_url": "http://www.mtn.ng",
                    "facebook_url": "https://facebook.com/MTNLoaded",
                    "founded_year": 2001,
                    "linkedin_uid": "2594006",
                    "linkedin_url": "http://www.linkedin.com/company/mtn-nigeria",
                    "alexa_ranking": 32134,
                    "angellist_url": null,
                    "primary_phone": {
                        "number": "+*********** 0180",
                        "source": "Owler",
                        "sanitized_number": "+*************"
                    },
                    "crunchbase_url": null,
                    "primary_domain": "mtn.ng",
                    "intent_strength": null,
                    "sanitized_phone": "+*************",
                    "organization_revenue": **********,
                    "intent_signal_account": null,
                    "owned_by_organization": {
                        "id": "54a12a6869702d9b8bd01902",
                        "name": "MTN",
                        "website_url": "http://www.mtn.com"
                    },
                    "publicly_traded_symbol": null,
                    "owned_by_organization_id": "54a12a6869702d9b8bd01902",
                    "publicly_traded_exchange": "other",
                    "has_intent_signal_account": false,
                    "organization_revenue_printed": "3.5B",
                    "organization_headcount_six_month_growth": 0.005119913769873349,
                    "organization_headcount_twelve_month_growth": 0.008108108108108109,
                    "organization_headcount_twenty_four_month_growth": 0.*****************
                },
                "normalizedData": {
                    "id": "54a1b8e974686940128e360b",
                    "name": "MTN Nigeria",
                    "phone": "<EMAIL>",
                    "company": "MTN Nigeria",
                    "photoUrl": "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/68aeb55379140f0001c3441d/picture",
                    "linkedinUrl": "http://www.linkedin.com/company/mtn-nigeria",
                    "companyDomain": "mtn.ng",
                    "isEmailVisible": false,
                    "isPhoneVisible": false,
                    "email": "<EMAIL>"
                },
                "searchHashes": [
                    "215933d3db89fe51772a794df3624aaab7bea519beafb69da9c7ae7a9d4d919a",
                    "caa66a01c732e3d06b92534cf564aefbc9c985921eda9b95cdfbfe1a7cf5c09b",
                    "5bcf6b0b50c96c9248d288e1ab669f2a4e048cfa4a6bf02df4ef04ee01d20f55"
                ],
                "email": null,
                "companyDomain": "mtn.ng",
                "name": "MTN Nigeria",