import { Action, Property, integrationFetch } from '@opendashboard-inc/integration-core';

type Inputs = {
  personId: string;
  revealEmails?: boolean;
  revealPhone?: boolean;
  webhookUrl?: string;
  skipUnlockCheck?: boolean; // Optional flag to bypass unlock checks
};

type Auth = {
  apiKey: string;
};

export const peopleEnrichment: Action<Inputs, Auth> = {
  identifier: 'peopleEnrichment',
  displayName: 'People Enrichment',
  description: 'Retrieve detailed information about a person by Apollo ID with optional email/phone reveals.',
  intent: 'read',
  props: {
    personId: Property.ShortText({
      displayName: 'Person ID',
      description: 'Apollo person ID to enrich',
      required: true,
    }),
    revealEmails: Property.Switch({
      displayName: 'Reveal Personal Emails',
      description: 'Set to true to reveal personal email addresses',
      required: false,
      defaultValue: false,
    }),
    revealPhone: Property.Switch({
      displayName: 'Reveal Phone Number',
      description: 'Set to true to reveal phone numbers (requires webhook)',
      required: false,
      defaultValue: false,
    }),
    webhookUrl: Property.ShortText({
      displayName: 'Webhook URL',
      description: 'Required for phone number reveals. Apollo will send phone data to this URL.',
      required: false,
    }),
    skipUnlockCheck: Property.Switch({
      displayName: 'Skip Unlock Check',
      description: 'Skip checking if data is already unlocked (use with caution)',
      required: false,
      defaultValue: false,
    }),
  },
  run: async ({ auth, propsValue }) => {
    try {
      // Parse personId if it's a JSON string and clean it
      let actualPersonId: string;
      try {
        const parsed = JSON.parse(propsValue.personId);
        actualPersonId = parsed.personId || propsValue.personId;
      } catch {
        actualPersonId = propsValue.personId;
      }

      // Clean the personId - remove extra spaces and quotes
      actualPersonId = actualPersonId.trim().replace(/^["']|["']$/g, '');
      
      console.log('🔓 [APOLLO ENRICHMENT] Raw personId:', JSON.stringify(propsValue.personId));
      console.log('🔓 [APOLLO ENRICHMENT] Cleaned personId:', actualPersonId);

      // Check if unlock is requested and if we should validate
      const isUnlockRequested = propsValue.revealEmails || propsValue.revealPhone;
      
      if (isUnlockRequested) {
        console.log('🔓 [APOLLO ENRICHMENT] ⚠️  Unlock requested - this will consume Apollo credits');
        
        // Check if data is already unlocked (unless skip flag is set)
        if (!propsValue.skipUnlockCheck) {
          console.log('🔓 [APOLLO ENRICHMENT] 🔍 Checking current unlock status...');
          
          // First, get current person data without unlock to check status
          const checkUrl = 'https://api.apollo.io/api/v1/people/match';
          const checkBody = { id: actualPersonId };
          
          const checkRes = await integrationFetch(checkUrl, {
            method: 'POST',
            headers: {
              'Cache-Control': 'no-cache',
              'Content-Type': 'application/json',
              'X-Api-Key': auth.apiKey,
            },
            body: JSON.stringify(checkBody),
          });

          if (!checkRes.ok) {
            const errorText = await checkRes.text();
            console.error('🔓 [APOLLO ENRICHMENT] Status Check Error:', checkRes.status, errorText);
            throw new Error(`Apollo API error during status check (${checkRes.status}): ${errorText}`);
          }

          const currentData = await checkRes.json();
          console.log('🔓 [APOLLO ENRICHMENT] Current status check result:', JSON.stringify(currentData, null, 2));

          // Check if requested data is already unlocked
          const person = currentData.person;
          if (person) {
            let alreadyUnlocked = [];
            
            // Check email unlock status
            if (propsValue.revealEmails) {
              // Check if personal emails are already revealed
              const hasPersonalEmails = person.email && !person.email.includes('*');
              if (hasPersonalEmails) {
                alreadyUnlocked.push('personal emails');
              }
            }
            
            // Check phone unlock status
            if (propsValue.revealPhone) {
              // Check if phone numbers are already revealed
              const hasRevealedPhone = person.phone_numbers && 
                person.phone_numbers.some((phone: any) => phone.number && !phone.number.includes('*'));
              if (hasRevealedPhone) {
                alreadyUnlocked.push('phone numbers');
              }
            }

            // Throw error if any requested data is already unlocked
            if (alreadyUnlocked.length > 0) {
              const unlocked = alreadyUnlocked.join(' and ');
              throw new Error(
                `❌ UNLOCK PREVENTED: ${unlocked} for person ID "${actualPersonId}" are already unlocked. ` +
                `This would waste Apollo credits. Use skipUnlockCheck=true to override this protection.`
              );
            }
          }
        } else {
          console.log('🔓 [APOLLO ENRICHMENT] ⚠️  Skipping unlock check as requested');
        }
        
        console.log('🔓 [APOLLO ENRICHMENT] ✅ Proceeding with unlock - data not already revealed');
      }

      // Build query parameters for Apollo API
      const queryParams = new URLSearchParams();
      
      if (propsValue.revealEmails) {
        queryParams.set('reveal_personal_emails', 'true');
      }
      
      if (propsValue.revealPhone) {
        queryParams.set('reveal_phone_number', 'true');
        if (!propsValue.webhookUrl) {
          throw new Error('Webhook URL is required when revealing phone numbers');
        }
      }

      // Build the full URL with query parameters
      const baseUrl = 'https://api.apollo.io/api/v1/people/match';
      const url = queryParams.toString() ? `${baseUrl}?${queryParams.toString()}` : baseUrl;

      // Build request body - Apollo /people/match DOES accept 'id'
      const requestBody: any = {
        id: actualPersonId
      };

      // Add webhook_url to request body (not query params) when revealing phone
      if (propsValue.revealPhone && propsValue.webhookUrl) {
        requestBody.webhook_url = propsValue.webhookUrl;
        console.log('🔓 [APOLLO ENRICHMENT] 📱 Phone unlock requested with webhook:', propsValue.webhookUrl);
        console.log('🔓 [APOLLO ENRICHMENT] 📱 Apollo will send phone data to this webhook URL');
        console.log('🔓 [APOLLO ENRICHMENT] 📱 Phone data will be delivered asynchronously (not in main response)');
      }

      console.log('🔓 [APOLLO ENRICHMENT] Calling Apollo API:');
      console.log('🔓 [APOLLO ENRICHMENT] URL:', url);
      console.log('🔓 [APOLLO ENRICHMENT] Body:', JSON.stringify(requestBody, null, 2));
      console.log('🔓 [APOLLO ENRICHMENT] Reveal Emails:', propsValue.revealEmails);
      console.log('🔓 [APOLLO ENRICHMENT] Reveal Phone:', propsValue.revealPhone);

      const res = await integrationFetch(url, {
        method: 'POST',
        headers: {
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/json',
          'X-Api-Key': auth.apiKey,
        },
        body: JSON.stringify(requestBody),
      });

      if (!res.ok) {
        const errorText = await res.text();
        console.error('🔓 [APOLLO ENRICHMENT] API Error:', res.status, errorText);
        throw new Error(`Apollo API error (${res.status}): ${errorText}`);
      }

      const result = await res.json();
      console.log('🔓 [APOLLO ENRICHMENT] Success Response:', JSON.stringify(result, null, 2));
      
      return result;
    } catch (error) {
      console.error('🔓 [APOLLO ENRICHMENT] Error:', error);
      return { error: (error as Error).message };
    }
  },
};