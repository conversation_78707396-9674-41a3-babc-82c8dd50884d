import { format, formatDistanceToNow } from "date-fns"
import { LeadMeta } from "@ui/typings/lead"

// Helper functions for lead generation components
export const getMetaProperty = (meta: LeadMeta | undefined, key: string): string | number | undefined => {
  if (!meta || typeof meta !== 'object') return undefined
  const value = meta[key]
  if (typeof value === 'string' || typeof value === 'number') return value
  if (Array.isArray(value)) return value.length
  return undefined
}

export const getMetaArrayProperty = (meta: LeadMeta | undefined, key: string): any[] | undefined => {
  if (!meta || typeof meta !== 'object') return undefined
  const value = meta[key]
  if (Array.isArray(value)) return value
  return undefined
}

export const getApolloProperty = (apolloData: any, key: string): any => {
  if (!apolloData || typeof apolloData !== 'object') return undefined
  return apolloData[key]
}

export const formatDate = (dateString: string | Date | undefined): string => {
  if (!dateString) return 'Not available'
  try {
    const date = new Date(dateString)
    return format(date, 'MMM dd, yyyy')
  } catch {
    return 'Invalid date'
  }
}

export const getTimeAgo = (dateString: string | Date | undefined): string => {
  if (!dateString) return 'Unknown'
  try {
    const date = new Date(dateString)
    return formatDistanceToNow(date, { addSuffix: true })
  } catch {
    return 'Unknown'
  }
}

// Helper function to format revenue
export const formatRevenue = (revenue: any): string => {
  if (!revenue) return 'N/A'
  if (typeof revenue === 'string') return revenue
  if (typeof revenue === 'number') {
    if (revenue >= 1000000000) return `$${(revenue / 1000000000).toFixed(1)}B`
    if (revenue >= 1000000) return `$${(revenue / 1000000).toFixed(1)}M`
    if (revenue >= 1000) return `$${(revenue / 1000).toFixed(1)}K`
    return `$${revenue.toLocaleString()}`
  }
  return 'N/A'
}

// Helper function to format growth percentage
export const formatGrowthPercentage = (growth: any): string => {
  if (growth === null || growth === undefined || growth === -1) return 'N/A'
  if (typeof growth === 'number') {
    return `${(growth * 100).toFixed(1)}%`
  }
  return 'N/A'
}

// Helper function to safely get nested object properties
export const getNestedProperty = (obj: any, path: string[]): any => {
  return path.reduce((current, key) => {
    if (current && typeof current === 'object' && key in current) {
      return current[key]
    }
    return undefined
  }, obj)
}

// Helper function to safely render text content
export const safeText = (value: any): string => {
  if (value === null || value === undefined) return 'N/A'
  if (typeof value === 'string') return value
  if (typeof value === 'number') return value.toString()
  if (typeof value === 'boolean') return value ? 'Yes' : 'No'
  if (Array.isArray(value)) return value.length.toString()
  if (typeof value === 'object') return 'Object'
  return 'N/A'
}

// Helper function to check if a value exists and is truthy
export const hasValue = (value: any): boolean => {
  return value !== null && value !== undefined && value !== '' && value !== 0
}
