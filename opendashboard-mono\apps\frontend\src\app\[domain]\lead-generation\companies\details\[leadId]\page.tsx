"use client"

import React from "react"
import { Details } from "@repo/ui/src/components/workspace/main/lead-generation/details/company"
import { useParams, useRouter } from "next/navigation"
import { useAuth } from "@ui/providers/user"
import { useWorkspace } from "@ui/providers/workspace"

const CompanyDetailsPage = () => {
    const params = useParams()
    const router = useRouter()
    const { token } = useAuth()
    const { workspace } = useWorkspace()
    
    const handleBack = () => {
        const domain = params.domain
        router.push(`/${domain}/lead-generation/companies/find-leads`)
    }

    // Don't render until we have the required data
    if (!token || !workspace?.workspace?.id) {
        return (
            <div className="flex-1 flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div>
        )
    }

    return (
        <Details 
            leadId={params.leadId as string}
            token={token.token}
            workspaceId={workspace.workspace.id}
            onBack={handleBack}
        />
    )
}

export default CompanyDetailsPage
