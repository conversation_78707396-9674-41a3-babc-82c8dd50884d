"use client"

import React, { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@ui/components/ui/button"
import { Input } from "@ui/components/ui/input"
import { Checkbox } from "@ui/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@ui/components/ui/table"
import { ScrollArea } from "@ui/components/ui/scroll-area"
import { PlusIcon, UserGroupIcon, EnvelopeIcon, PhoneIcon, EllipsisVerticalIcon, TrashIcon, ChevronRightIcon, ChartLineIcon, DatabaseIcon, CodeMergeIcon, UpRightFromSquareIcon, MagnifyingGlassIcon } from "@ui/components/icons/FontAwesomeRegular"
import { MagnifyingGlassCircleIcon } from "@heroicons/react/24/outline"
import { CompanyFilter } from "@ui/components/workspace/main/lead-generation/filters/company"
import { UnlockEmailModal } from "@ui/components/workspace/main/common/unlockEmail"
import { UnlockPhoneModal } from "@ui/components/workspace/main/common/unlockPhone"
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/ui/popover"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuGroup } from "@ui/components/ui/dropdown-menu"
import { searchCompanyLeads } from "@ui/api/leads"
import { SearchFilters, SearchLeadsRequest, Lead as APILead, NormalizedLeadData } from "@ui/typings/lead"
import { useWorkspace } from "@ui/providers/workspace"
import { useAuth } from "@ui/providers/user"
import { useAlert } from "@ui/providers/alert"
import { useRouter, useParams } from "@ui/context/routerContext"

// UI Lead interface for display
interface Lead {
    id: string
    name: string
    jobTitle: string
    company: string
    email: string
    phone: string
    links: string
}

interface FindLeadsProps {
    onLeadCreated?: (lead: any) => void
    sidebarState?: {
        isOpen: boolean
        setIsOpen: (open: boolean) => void
    }
}

// ActionButton Component
const ActionButton = ({ 
    icon: Icon, 
    children, 
    onClick 
}: { 
    icon: React.ComponentType<{ className?: string }>; 
    children: React.ReactNode; 
    onClick: () => void; 
}) => (
    <Button
        variant="outline"
        size="sm"
        onClick={onClick}
        className="text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center"
    >
        <Icon className="size-3" />
        <span className="truncate">{children}</span>
    </Button>
)

// ViewLinksModal Component
interface ContactLink {
  id: string
  title: string
  url: string
}

interface ViewLinksModalProps {
  trigger: React.ReactNode
  links: ContactLink[]
}

const ViewLinksModal = ({ trigger, links }: ViewLinksModalProps) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        {trigger}
      </PopoverTrigger>
      <PopoverContent
        className="w-64 p-0 rounded-none border border-neutral-200 bg-white shadow-lg"
        align="end"
        sideOffset={4}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-neutral-200">
          <h3 className="text-xs font-semibold text-black">Contact links</h3>
        </div>

        {/* Links List */}
        <div className="p-0">
          {links.map((link) => (
            <a
              key={link.id}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-between p-3 hover:bg-neutral-50 transition-colors cursor-pointer"
            >
              <span className="text-xs text-primary font-semibold">{link.title}</span>
              <UpRightFromSquareIcon className="size-3 text-neutral-400" />
            </a>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  )
}

// LeadActionsDropdown Component
interface LeadActionsDropdownProps {
  trigger: React.ReactNode
  onSendEmail?: () => void
  onAddToSegments?: () => void
  onAddToDatabase?: () => void
  onAddToWorkflow?: () => void
}

const LeadActionsDropdown = ({ 
  trigger, 
  onSendEmail, 
  onAddToSegments, 
  onAddToDatabase, 
  onAddToWorkflow 
}: LeadActionsDropdownProps) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        {trigger}
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        className="w-56 rounded-none text-neutral-800 font-semibold"
        align="end"
        sideOffset={4}
      >
        <DropdownMenuGroup className="p-1 flex flex-col gap-2">
          <DropdownMenuItem 
            className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer"
            onClick={onSendEmail}
          >
            <EnvelopeIcon className="size-3 text-neutral-600" />
            <span>Send Email</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer"
            onClick={onAddToSegments}
          >
            <ChartLineIcon className="size-3 text-neutral-600" />
            <span>Add to Segments</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer"
            onClick={onAddToDatabase}
          >
            <DatabaseIcon className="size-3 text-neutral-600" />
            <span>Add to Database</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer"
            onClick={onAddToWorkflow}
          >
            <CodeMergeIcon className="size-3 text-neutral-600" />
            <span>Add to Workflow</span>
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

const FindLeads = ({ onLeadCreated, sidebarState }: FindLeadsProps) => {
    const router = useRouter()
    const params = useParams()
    const { workspace } = useWorkspace()
    const { token } = useAuth()
    const { toast } = useAlert()
    
    // State management - Real API data only
    const [leads, setLeads] = useState<Lead[]>([])
    const [selectedLeads, setSelectedLeads] = useState<string[]>([])
    const [isSearching, setIsSearching] = useState(false)
    const [hasSearched, setHasSearched] = useState(false)
    const [searchResults, setSearchResults] = useState<any>(null)
    
    // Search filters state - ADDED
    const [searchFilters, setSearchFilters] = useState<SearchFilters>({
        person: {},
        company: {},
        signals: {},
        customFilters: {}
    })
    
    // Exclude my leads state - ADDED
    const [excludeMyLeads, setExcludeMyLeads] = useState(false)
    
    // Pagination state
    const [currentPage, setCurrentPage] = useState(1)
    const [totalPages, setTotalPages] = useState(1)
    const [totalCount, setTotalCount] = useState(0)
    
    // Modal states
    const [emailModalOpen, setEmailModalOpen] = useState(false)
    const [phoneModalOpen, setPhoneModalOpen] = useState(false)
    const [selectedLeadId, setSelectedLeadId] = useState<string | null>(null)
    
    // Initialize searchFilters with default values when component mounts
    React.useEffect(() => {
        const defaultFilters: SearchFilters = {
            person: {},
            company: {},
            signals: {},
            customFilters: {}
        }
        setSearchFilters(defaultFilters) // FIXED: Now properly sets the state
        console.log(`🔍 [FRONTEND DEBUG] Initialized searchFilters with defaults:`, JSON.stringify(defaultFilters, null, 2))
    }, [])
    
    // Search functionality
    const searchLeads = async (filters: SearchFilters = searchFilters, page: number = 1) => {
        console.log(`🔍 [FRONTEND DEBUG] searchLeads called`);
        console.log(`🔍 [FRONTEND DEBUG] Input filters:`, JSON.stringify(filters, null, 2));
        console.log(`🔍 [FRONTEND DEBUG] Current searchFilters state:`, JSON.stringify(searchFilters, null, 2));
        
        if (!workspace?.workspace?.id || !token?.token) {
            toast.error("Authentication required")
            return
        }

        // Check if API is properly configured
        if (!process.env.NEXT_PUBLIC_API_URL) {
            toast.error("Search service is currently unavailable. Please try again later.")
            return
        }

        setIsSearching(true)
        try {
            // Clean filters before sending - excludeMyLeads is NOT a search filter
            const cleanFilters = { ...filters };
            // excludeMyLeads is already a separate state variable, no need to delete from filters
            
            // Proper frontend pagination through accumulated results
            // Calculate how many pages we have available from previous searches
            // const totalPagesAvailable = searchResults ? Math.ceil(searchResults.totalCount / 50) : 1; // Removed
            // const currentPage = searchResults && searchResults.hasNextPage ? 
            //     ((pageCounter % totalPagesAvailable) + 1) : 1; // Removed
            
            // setPageCounter(prev => prev + 1) // Removed
            
            console.log(`🔍 [FRONTEND DEBUG] 🔧 FILTERS PREPARATION:`);
            console.log(`🔍 [FRONTEND DEBUG] - Search filters: ${JSON.stringify(filters, null, 2)}`);
            console.log(`🔍 [FRONTEND DEBUG] - excludeMyLeads (separate): ${excludeMyLeads}`);
            console.log(`🔍 [FRONTEND DEBUG] - excludeMyLeads is NOT part of search criteria`);
            // console.log(`🔍 [FRONTEND DEBUG] - Total pages available: ${totalPagesAvailable}`); // Removed
            // console.log(`🔍 [FRONTEND DEBUG] - Page counter: ${pageCounter + 1}, Requesting page: ${currentPage}`); // Removed
            console.log(`🔍 [FRONTEND DEBUG] ==========================================`);
            
            const searchRequest: SearchLeadsRequest = {
                filters: cleanFilters,
                excludeMyLeads: excludeMyLeads, // FIXED: Now uses the state variable
                pagination: {
                    page: page, // Always page 1 for now
                    limit: 50
                }
            }

            console.log(`🔍 [FRONTEND DEBUG] Search query: ""`); // searchQuery removed
            console.log(`🔍 [FRONTEND DEBUG] Workspace ID: ${workspace.workspace.id}`);
            console.log(`🔍 [FRONTEND DEBUG] Token exists: ${!!token.token}`);
            console.log(`🔍 [FRONTEND DEBUG] API URL: ${process.env.NEXT_PUBLIC_API_URL}`);
            console.log(`🔍 [FRONTEND DEBUG] 🚀 Sending search request to API:`, JSON.stringify(searchRequest, null, 2));
            console.log(`🔍 [FRONTEND DEBUG] Calling searchCompanyLeads API function...`);

            const response = await searchCompanyLeads(token.token, workspace.workspace.id, searchRequest)
            
            console.log(`🔍 [FRONTEND DEBUG] API response received:`, response);
            
            if (response.error) {
                // Provide user-friendly error messages
                const errorMessage = response.error.toLowerCase()
                if (errorMessage.includes('unauthorized') || errorMessage.includes('authentication')) {
                    toast.error("Session expired. Please log in again.")
                } else if (errorMessage.includes('network') || errorMessage.includes('connection')) {
                    toast.error("Connection error. Please check your internet and try again.")
                } else if (errorMessage.includes('env') || errorMessage.includes('undefined')) {
                    toast.error("Search service is currently unavailable. Please try again later.")
                } else {
                    toast.error("Failed to search company leads. Please try again.")
                }
                return
            }

            const apiLeads = response.data?.data?.leads || []
            
            console.log(`🔍 [FRONTEND DEBUG] ✅ API leads extracted:`, {
                leadsCount: apiLeads.length,
                firstLead: apiLeads[0] || null,
                allLeads: apiLeads
            });
            
            // Convert API leads to UI format
            const convertedLeads: Lead[] = apiLeads.map((apiLead) => ({
                id: apiLead.id,
                name: apiLead.normalizedData.name,
                jobTitle: apiLead.normalizedData.jobTitle || "",
                company: apiLead.normalizedData.company || "",
                email: apiLead.normalizedData.isEmailVisible ? apiLead.normalizedData.email || "unlock" : "unlock",
                phone: apiLead.normalizedData.isPhoneVisible ? apiLead.normalizedData.phone || "unlock" : "unlock",
                links: "view"
            }))

            console.log(`🔍 [FRONTEND DEBUG] Converted leads for UI:`, {
                convertedCount: convertedLeads.length,
                firstConverted: convertedLeads[0] || null
            });

            setLeads(convertedLeads)
            // Store search results with proper structure for pagination display
            setSearchResults({
                totalCount: response.data?.data?.totalCount,
                metadata: response.data?.data?.metadata || {},
                hasNextPage: response.data?.data?.hasNextPage
            })
            
            // Update pagination state
            const responseTotalCount = response.data?.data?.totalCount || 0
            const responseHasNextPage = response.data?.data?.hasNextPage || false
            
            // Calculate total pages based on cached results
            // Show ALL cached pages + ONE page to trigger Apollo expansion
            let availablePages = 1
            
            // Use the backend's totalPagesAvailable to show all cached pages
            // This ensures users can navigate to ALL available pages, plus one more to trigger Apollo
            // Example: If backend has 14 pages, show 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15
            // Where page 15 triggers Apollo to get more leads
            const totalPagesAvailable = response.data?.data?.metadata?.totalPagesAvailable || 1
            availablePages = totalPagesAvailable + 1
            
            setTotalCount(responseTotalCount)
            setTotalPages(availablePages)
            setCurrentPage(page) // Set to the page we just searched for
            
            console.log(`🔍 [FRONTEND DEBUG] 📊 Pagination updated:`, {
                currentPage: page,
                totalPages: availablePages,
                totalCount: responseTotalCount,
                leadsPerPage: 50
            })
            
            setHasSearched(true)
            toast.success(`Found ${convertedLeads.length} company leads`)
            
            console.log(`🔍 [FRONTEND DEBUG] ✅ Search completed successfully. State updated.`);
            
        } catch (error) {
            console.error("Search error:", error)
            // Provide user-friendly error message without exposing technical details
            toast.error("Search service is temporarily unavailable. Please try again later.")
        } finally {
            setIsSearching(false)
            console.log(`🔍 [FRONTEND DEBUG] Search state reset to not searching`);
        }
    }
    
    // Handle filter changes from CompanyFilter
    const handleFilterChange = (filters: SearchFilters) => {
        console.log(`🔍 [FRONTEND DEBUG] handleFilterChange called with:`, JSON.stringify(filters, null, 2));
        console.log(`🔍 [FRONTEND DEBUG] Previous searchFilters:`, JSON.stringify(searchFilters, null, 2));
        
        // Reset pagination when filters change - this ensures fresh results from page 1
        // when user changes search criteria (industry, company size, etc.)
        setCurrentPage(1)
        setTotalPages(1)
        setTotalCount(0)
        console.log(`🔍 [FRONTEND DEBUG] 🔄 Pagination reset to page 1 (filters changed)`);
        
        // Simply replace the filters completely - CompanyFilter sends the complete state
        // No need to merge as it can cause conflicts and state inconsistencies
        setSearchFilters(filters) // FIXED: Now properly updates the state
        
        console.log(`🔍 [FRONTEND DEBUG] searchFilters state updated to:`, JSON.stringify(filters, null, 2));
        // Don't auto-search! Let user control search via Search button
        // This provides a much cleaner UX flow
    }
    
    // Pagination functions
    const handlePageChange = (page: number) => {
        console.log(`🔍 [FRONTEND DEBUG] Page change requested: ${page}`);
        if (page < 1) {
            console.log(`🔍 [FRONTEND DEBUG] ❌ Invalid page: ${page} - cannot be less than 1`);
            return
        }
        
        // Allow clicking beyond current totalPages - this will trigger Apollo calls
        // when the backend detects the requested page doesn't exist in cache
        if (page > totalPages) {
            console.log(`🔍 [FRONTEND DEBUG] 🔄 Page ${page} requested beyond current cache (${totalPages} pages)`);
            console.log(`🔍 [FRONTEND DEBUG] This will trigger Apollo call to get more leads`);
        }
        
        // Just set the current page - don't search yet
        // User needs to click Search button to actually search for that page
        setCurrentPage(page)
        console.log(`🔍 [FRONTEND DEBUG] ✅ Page set to ${page}. Now click Search button to get results for page ${page}`);
    }
    
    const calculateTotalPages = (totalLeads: number, leadsPerPage: number = 50) => {
        return Math.ceil(totalLeads / leadsPerPage)
    }
    
    const generatePageNumbers = () => {
        const pages = []
        const maxVisiblePages = 5 // Show max 5 page numbers at once
        
        if (totalPages <= maxVisiblePages) {
            // Show all pages if total is small
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i)
            }
        } else {
            // Show smart pagination with ellipsis
            if (currentPage <= 3) {
                // Near start: show 1, 2, 3, 4, 5, ..., last
                for (let i = 1; i <= 5; i++) {
                    pages.push(i)
                }
                pages.push('...')
                pages.push(totalPages)
            } else if (currentPage >= totalPages - 2) {
                // Near end: show 1, ..., last-4, last-3, last-2, last-1, last
                pages.push(1)
                pages.push('...')
                for (let i = totalPages - 4; i <= totalPages; i++) {
                    pages.push(i)
                }
            } else {
                // Middle: show 1, ..., current-1, current, current+1, ..., last
                pages.push(1)
                pages.push('...')
                for (let i = currentPage - 1; i <= currentPage + 1; i++) {
                    pages.push(i)
                }
                pages.push('...')
                pages.push(totalPages)
            }
        }
        
        return pages
    }
    
    // Manual search trigger
    const handleSearch = () => {
        console.log(`🔍 [FRONTEND DEBUG] handleSearch called`);
        console.log(`🔍 [FRONTEND DEBUG] Current searchFilters:`, JSON.stringify(searchFilters, null, 2));
        console.log(`🔍 [FRONTEND DEBUG] Current page: ${currentPage}`);
        console.log(`🔍 [FRONTEND DEBUG] Current searchQuery: ""`); // searchQuery removed
        
        // Use the current page that user selected (don't reset to 1)
        // This allows users to click page 2, then click Search to get page 2 results
        
        // ONLY send sidebar filters - ignore search input field
        // Search input is for a different purpose, not for Apollo searches
        const filtersToSend = {
            ...searchFilters // FIXED: Now uses the actual searchFilters state
        };
        
        console.log(`🔍 [FRONTEND DEBUG] Final filters being sent to searchLeads (sidebar filters only):`, JSON.stringify(filtersToSend, null, 2));
        console.log(`🔍 [FRONTEND DEBUG] Searching for page: ${currentPage}`);
        searchLeads(filtersToSend, currentPage) // Use current page, not always page 1
    }
    
    // Event handlers
    const handleCreateLead = () => {
        console.log("Create company lead clicked")
    }
    
    const handleImportLeads = () => {
        console.log("Import company leads clicked")
    }
    
    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            setSelectedLeads(leads.map(lead => lead.id))
        } else {
            setSelectedLeads([])
        }
    }
    
    const handleSelectLead = (leadId: string, checked: boolean) => {
        if (checked) {
            setSelectedLeads([...selectedLeads, leadId])
        } else {
            setSelectedLeads(selectedLeads.filter(id => id !== leadId))
        }
    }
    
    const handleUnlockEmail = (leadId: string) => {
        setSelectedLeadId(leadId)
        setEmailModalOpen(true)
    }
    
    const handleUnlockPhone = (leadId: string) => {
        setSelectedLeadId(leadId)
        setPhoneModalOpen(true)
    }
    
    // Generate contact links based on lead data
    const getContactLinks = (lead: Lead) => {
        const links = []
        
        // Add LinkedIn if available (could be from API data)
        if (lead.name) {
            links.push({ 
                id: "linkedin", 
                title: "LinkedIn Profile", 
                url: `https://linkedin.com/search/results/people/?keywords=${encodeURIComponent(lead.name + ' ' + lead.company)}` 
            })
        }
        
        // Add company website if available
        if (lead.company) {
            links.push({ 
                id: "company", 
                title: "Company Website", 
                url: `https://www.google.com/search?q=${encodeURIComponent(lead.company + ' official website')}` 
            })
        }
        
        // Add company LinkedIn
        if (lead.company) {
            links.push({ 
                id: "company-linkedin", 
                title: "Company LinkedIn", 
                url: `https://linkedin.com/search/results/companies/?keywords=${encodeURIComponent(lead.company)}` 
            })
        }
        
        return links
    }

    const handleNameClick = (lead: Lead) => {
        // Navigate to company details page using router
        const domain = params.domain
        router.push(`/${domain}/lead-generation/companies/details/${lead.id}`)
    }
    
    const filteredLeads = useMemo(() => {
        // if (!searchQuery?.trim()) return leads // searchQuery removed
        // const searchTerm = searchQuery.trim().toLowerCase() // searchQuery removed
        return leads // searchQuery removed
    }, [leads]) // searchQuery removed

    return (
        <>
            <div className="flex items-center justify-between px-3 h-10 border-b border-neutral-200 bg-white">
                <div className="flex items-center">
                    <h1 className="text-xs font-semibold text-black">Find Companies</h1>
                </div>
                <div className="flex items-center space-x-2">
                    {selectedLeads.length > 0 && (
                        <Button variant="ghost" className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium text-red-600 hover:text-red-700 hover:bg-red-50 cursor-pointer" onClick={() => { setLeads(leads.filter(lead => !selectedLeads.includes(lead.id))); setSelectedLeads([]) }}>
                            <TrashIcon className="size-3"/>Delete {selectedLeads.length}
                        </Button>
                    )}
                    {/* Search Input with Button */}
                    <div className="flex items-center gap-2">
                        {/* Inline Pagination - Left side */}
                        {hasSearched && (
                            <div className="flex items-center gap-2">
                                {/* Results info - Removed page display text */}
                                
                                {/* Pagination controls */}
                                {totalPages > 1 ? (
                                    <div className="flex items-center gap-1">
                                        {/* Previous button */}
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                            disabled={currentPage === 1}
                                            className="h-6 w-6 p-0 text-xs"
                                        >
                                            ←
                                        </Button>
                                        
                                        {/* Page numbers */}
                                        <div className="flex items-center gap-1">
                                            {generatePageNumbers().map((page, index) => (
                                                <React.Fragment key={index}>
                                                    {page === '...' ? (
                                                        <span className="px-1 text-neutral-400 text-xs">...</span>
                                                    ) : (
                                                        <Button
                                                            variant={currentPage === page ? "default" : "outline"}
                                                            size="sm"
                                                            onClick={() => setCurrentPage(page as number)}
                                                            className={`h-6 w-6 p-0 text-xs ${
                                                                currentPage === page 
                                                                    ? 'bg-primary text-white' 
                                                                    : 'hover:bg-neutral-50'
                                                            }`}
                                                        >
                                                            {page}
                                                        </Button>
                                                    )}
                                                </React.Fragment>
                                            ))}
                                        </div>
                                        
                                        {/* Next button */}
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                            disabled={currentPage === totalPages}
                                            className="h-6 w-6 p-0 text-xs"
                                        >
                                            →
                                        </Button>
                                    </div>
                                ) : (
                                    <div className="text-xs text-neutral-500">
                                        Single page
                                    </div>
                                )}
                            </div>
                        )}
                        
                        <div className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start flex hover:bg-neutral-100 focus:bg-neutral-100 active:bg-neutral-100 items-center whitespace-nowrap font-medium cursor-pointer">
                            <MagnifyingGlassCircleIcon className="size-4"/>
                            <Input 
                                placeholder="Search and find companies" 
                                // value={searchQuery} // searchQuery removed
                                // onChange={(e) => setSearchQuery(e.target.value)} // searchQuery removed
                                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                                className="text-xs transition-all outline-none h-auto !p-0 !ring-0 w-12 focus:w-48 !bg-transparent border-0 shadow-none drop-shadow-none placeholder:text-muted-foreground" 
                            />
                        </div>
                        <Button 
                            size="sm" 
                            onClick={handleSearch}
                            disabled={isSearching}
                            className="text-xs rounded-full h-auto px-3 py-1.5"
                        >
                            {isSearching ? "Searching..." : "Search"}
                        </Button>
                    </div>
                </div>
            </div>
            
            {/* Main Content Area */}
            <div className="flex-1 flex overflow-hidden">
                {/* Sidebar Filter - Always visible for find-leads */}
                <CompanyFilter 
                    forceSidebar={true}
                    onFilterChange={handleFilterChange}
                    excludeMyLeads={excludeMyLeads} // FIXED: Now uses the state variable
                    onExcludeMyLeadsChange={(value) => {
                        console.log(`🔒 [FRONTEND DEBUG] Parent received excludeMyLeads change:`);
                        console.log(`🔒 [FRONTEND DEBUG] - Previous value: ${excludeMyLeads}`);
                        console.log(`🔒 [FRONTEND DEBUG] - New value: ${value}`);
                        console.log(`🔒 [FRONTEND DEBUG] - This will affect display filtering, not search criteria`);
                        console.log(`🔒 [FRONTEND DEBUG] ==========================================`);
                        
                        setExcludeMyLeads(value) // FIXED: Now properly updates the state
                        console.log(`🔒 [FRONTEND DEBUG] excludeMyLeads state updated to: ${value}`);
                    }}
                />
                
                {/* Table Container */}
                <div className="flex-1 overflow-hidden">
                    {filteredLeads.length === 0 ? (
                        /* Empty State - INSIDE the container */
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                                <MagnifyingGlassIcon className="size-12 text-neutral-400 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-neutral-900 mb-2">
                                    {hasSearched ? "No companies found" : "Ready to find companies"}
                                </h3>
                                <p className="text-neutral-500 mb-4 text-sm">
                                    {hasSearched 
                                        ? "Try adjusting your search query or filters to find more results" 
                                        : "Use the search box above or apply filters on the left to discover companies. Click the Search button to start."
                                    }
                                </p>
                            </div>
                        </div>
                    ) : (
                        <>
                            {/* Table with Results */}
                            <ScrollArea className="size-full scrollBlockChild">
                        <Table>
                        <TableHeader>
                            <TableRow className="border-b border-neutral-200 bg-white sticky top-0 z-10">
                                <TableHead className="w-12 h-10 px-3">
                                    <Checkbox checked={selectedLeads.length === filteredLeads.length && filteredLeads.length > 0} onCheckedChange={handleSelectAll} />
                                </TableHead>
                                <TableHead className="h-10 px-1 text-left font-bold text-black">Name</TableHead>
                                <TableHead className="h-10 px-1 text-left font-bold text-black">Job title</TableHead>
                                <TableHead className="h-10 px-1 text-left font-bold text-black">Company</TableHead>
                                <TableHead className="h-10 px-1 text-left font-bold text-black">Email</TableHead>
                                <TableHead className="h-10 px-1 text-left font-bold text-black">Phone number</TableHead>
                                <TableHead className="h-10 px-1 text-left font-bold text-black">Social Links</TableHead>
                                <TableHead className="w-12 h-10 px-1"></TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredLeads.map((lead) => (
                                <TableRow key={lead.id} className="border-b border-neutral-200 hover:bg-neutral-50 transition-colors group">
                                    <TableCell className="w-12 px-3 relative">
                                        <Checkbox checked={selectedLeads.includes(lead.id)} onCheckedChange={(checked: boolean) => handleSelectLead(lead.id, checked)} className={`${selectedLeads.includes(lead.id) ? 'opacity-100 visible' : 'invisible opacity-0 group-hover:opacity-100 group-hover:visible'}`} />
                                    </TableCell>
                                    <TableCell className="px-1">
                                        <button 
                                            className="text-primary hover:text-primary/80 font-semibold text-xs cursor-pointer hover:border-b hover:border-neutral-600 bg-transparent border-none p-0" 
                                            onClick={() => handleNameClick(lead)}
                                        >
                                            {lead.name}
                                        </button>
                                    </TableCell>
                                    <TableCell className="px-1 text-xs text-muted-foreground">{lead.jobTitle}</TableCell>
                                    <TableCell className="px-1 text-xs text-muted-foreground">{lead.company}</TableCell>
                                    <TableCell className="px-1">
                                        {lead.email === "unlock" ? (
                                            <ActionButton icon={EnvelopeIcon} onClick={() => handleUnlockEmail(lead.id)}>Unlock Email</ActionButton>
                                        ) : (
                                            <span className="text-xs text-neutral-700">{lead.email}</span>
                                        )}
                                    </TableCell>
                                    <TableCell className="px-1">
                                        {lead.phone === "unlock" ? (
                                            <ActionButton icon={PhoneIcon} onClick={() => handleUnlockPhone(lead.id)}>Unlock Mobile</ActionButton>
                                        ) : (
                                            <span className="text-xs text-neutral-700">{lead.phone}</span>
                                        )}
                                    </TableCell>
                                    <TableCell className="px-1">
                                        <ViewLinksModal 
                                            trigger={
                                                <button className="text-primary hover:text-primary/80 font-semibold text-xs flex items-center gap-1 cursor-pointer bg-transparent border-none p-0">
                                                    View links<ChevronRightIcon className="size-3" />
                                                </button>
                                            } 
                                            links={getContactLinks(lead)} 
                                        />
                                    </TableCell>
                                    <TableCell className="w-12 px-2 relative">
                                        <LeadActionsDropdown trigger={<Button variant="ghost" size="sm" className="h-7 w-7 p-0 text-neutral-400 hover:text-neutral-600 invisible opacity-0 group-hover:opacity-100 group-hover:visible"><EllipsisVerticalIcon className="size-4" /></Button>} onSendEmail={() => {}} onAddToSegments={() => {}} onAddToDatabase={() => {}} onAddToWorkflow={() => {}} />
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                    {/* Horizontal line under table */}
                    <div className="border-b border-neutral-200"></div>
                </ScrollArea>
                
                
                </>
                    )}
                </div>
            </div>
        
            <UnlockEmailModal 
                open={emailModalOpen} 
                onOpenChange={setEmailModalOpen} 
                leadId={selectedLeadId}
            />
            <UnlockPhoneModal 
                open={phoneModalOpen} 
                onOpenChange={setPhoneModalOpen} 
                leadId={selectedLeadId}
            />
        </>
    )
}

export default FindLeads
