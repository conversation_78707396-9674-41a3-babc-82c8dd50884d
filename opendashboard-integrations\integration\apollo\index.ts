import { IntegrationDefinition } from '@opendashboard-inc/integration-core';
import { searchPeople } from './actions/searchPeople';
import { getPerson } from './actions/getPerson';
import { createContact } from './actions/createContact';
import { peopleEnrichment } from './actions/peopleEnrichment';
import { bulkPeopleEnrichment } from './actions/bulkPeopleEnrichment';
import { organizationEnrichment } from './actions/organizationEnrichment';
import { bulkOrganizationEnrichment } from './actions/bulkOrganizationEnrichment';
import { customApiCall } from './actions/customApiCall';
import { advancedPeopleSearch } from './actions/advancedPeopleSearch';
import { searchOrganizations } from './actions/searchOrganizations';


const ApolloIntegration: IntegrationDefinition = {
    name: 'apollo',
    displayName: 'Apollo.io',
    icon: 'https://opendashboard.app/assets/integrations/apollo.svg',
    description: 'Enrich and search for contacts via Apollo.io',
    auth: {
        type: 'custom',
        fields: {
            apiKey: {
                label: 'Apollo API Key',
                type: 'password',
                description: 'You can find this in your Apollo.io account settings.',
            },
        },
        verify: async () => true, // fallback until you get upgraded access
    },
    actions: {
        searchPeople,
        getPerson,
        createContact,
        peopleEnrichment,
        bulkPeopleEnrichment,
        organizationEnrichment,
        bulkOrganizationEnrichment,
        customApiCall,
        advancedPeopleSearch,
        searchOrganizations,

    },
    triggers: {},
};

export default ApolloIntegration;
