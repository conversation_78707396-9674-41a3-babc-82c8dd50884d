"use client"

import React, { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader } from "@ui/components/ui/dialog"
import { Button } from "@ui/components/ui/button"
import { Cross2Icon } from "@radix-ui/react-icons"
import { EnvelopeIcon, CheckIcon } from "@ui/components/icons/FontAwesomeRegular"
import { unlockLead } from "@ui/api/leads"
import { useWorkspace } from "@ui/providers/workspace"
import { useAuth } from "@ui/providers/user"
import { useAlert } from "@ui/providers/alert"

interface UnlockEmailModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  leadId: string
  onUnlockSuccess?: (unlockedLead: any) => void
}

export const UnlockEmailModal = ({ open, onOpenChange, leadId, onUnlockSuccess }: UnlockEmailModalProps) => {
  const { workspace } = useWorkspace()
  const { token } = useAuth()
  const { toast } = useAlert()
  
  const [unlocking, setUnlocking] = useState(false)
  const [unlocked, setUnlocked] = useState(false)

  const handleUnlock = async () => {
    if (!workspace?.workspace?.id || !token?.token) {
      toast.error("Authentication required")
      return
    }

    setUnlocking(true)
    try {
      const response = await unlockLead(token.token, workspace.workspace.id, leadId, {
        unlockType: 'email'
      })

      if (response.error) {
        toast.error(response.error || "Failed to unlock email")
        return
      }

      const data = response.data.data
      setUnlocked(true)
      
      if (data.alreadyUnlocked) {
        toast.info("Email was already unlocked")
      } else {
        toast.success(`Email unlocked successfully! Used ${data.unlock.creditsUsed} credits.`)
      }

      // Call the success callback to update parent component
      onUnlockSuccess?.(data.lead)
      
      // Close modal after a short delay
      setTimeout(() => {
        onOpenChange(false)
        setUnlocked(false)
      }, 2000)
      
    } catch (error) {
      console.error("Unlock error:", error)
      toast.error("Failed to unlock email")
    } finally {
      setUnlocking(false)
    }
  }

  const handleClose = () => {
    if (!unlocking) {
      onOpenChange(false)
      setUnlocked(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="w-[500px] max-w-[90vw] p-0 rounded-none border border-neutral-200 bg-white shadow-lg" hideCloseBtn>
        {/* Close Button */}
        <div className="absolute top-4 right-4 z-10">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-neutral-400 hover:text-neutral-600 cursor-pointer"
            onClick={handleClose}
            disabled={unlocking}
          >
            <Cross2Icon className="size-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-10 text-center">
          {/* Illustration */}
          <div className="mb-8 flex justify-center">
            {unlocked ? (
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
                <CheckIcon className="w-12 h-12 text-green-600" />
              </div>
            ) : (
              <div className="relative">
                {/* Padlock */}
                <div className="w-20 h-20 border-2 border-neutral-900 rounded-lg relative">
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-5 h-7 border-2 border-neutral-900 rounded-t-lg"></div>
                  <div className="absolute top-7 left-1/2 transform -translate-x-1/2 w-2.5 h-2.5 bg-neutral-900 rounded-full"></div>
                </div>
                
                {/* Key */}
                <div className="absolute -right-5 top-10 transform rotate-45">
                  <div className="w-10 h-1 bg-neutral-900 rounded-full"></div>
                  <div className="absolute -top-1 -right-1 w-4 h-4 border-2 border-neutral-900 rounded-full"></div>
                  <div className="absolute -top-1 -right-1 w-1.5 h-1.5 bg-neutral-900 rounded-full"></div>
                </div>
                
                {/* Sparkles */}
                <div className="absolute -top-3 -left-3 w-2.5 h-2.5 bg-neutral-900 transform rotate-45"></div>
                <div className="absolute -top-2 -right-2 w-1.5 h-1.5 bg-neutral-900 transform rotate-45"></div>
                <div className="absolute -bottom-2 -left-2 w-1.5 h-1.5 bg-neutral-900 transform rotate-45"></div>
              </div>
            )}
          </div>

          {/* Heading */}
          <h2 className="text-2xl font-semibold text-black mb-4">
            {unlocked ? "Email Unlocked!" : "Unlock this email address"}
          </h2>

          {/* Description */}
          <p className="text-xs text-muted-foreground mb-8 leading-relaxed">
            {unlocked 
              ? "The email address has been successfully unlocked. You can now view and use this contact information."
              : "This contact's email is premium. Unlock it to view and take action immediately."
            }
          </p>

          {/* Call to Action Button */}
          {unlocked ? (
            <div className="flex items-center justify-center gap-2 text-green-600">
              <CheckIcon className="w-5 h-5" />
              <span className="text-sm font-medium">Successfully Unlocked</span>
            </div>
          ) : (
            <Button
              onClick={handleUnlock}
              disabled={unlocking}
              className="w-full h-14 bg-neutral-900 hover:bg-neutral-800 text-white font-semibold rounded-full text-lg cursor-pointer disabled:opacity-50"
            >
              {unlocking ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  Unlocking...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <EnvelopeIcon className="w-5 h-5" />
                  Unlock Email
                </div>
              )}
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
